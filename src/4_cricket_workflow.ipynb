{"cells": [{"cell_type": "code", "execution_count": 19, "id": "a7feb6de", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph, START, END\n", "from typing import TypedDict\n"]}, {"cell_type": "code", "execution_count": 20, "id": "3303fdb3", "metadata": {}, "outputs": [], "source": ["class BatsmanState(TypedDict):\n", "    runs: int\n", "    balls: int\n", "    fours: int\n", "    sixes: int\n", "    sr: float\n", "    bpb: float\n", "    boundary_percent: float\n", "    summary: str"]}, {"cell_type": "code", "execution_count": 21, "id": "510ed2db", "metadata": {}, "outputs": [], "source": ["def calculate_sr(state: BatsmanState) -> BatsmanState:\n", "    sr = (state[\"runs\"] / state[\"balls\"]) * 100\n", "    \n", "    return {\"sr\": sr}"]}, {"cell_type": "code", "execution_count": 22, "id": "2b455f27", "metadata": {}, "outputs": [], "source": ["def calculate_bpb(state: BatsmanState) -> BatsmanState:\n", "    bpb = state[\"balls\"] / (state[\"fours\"] + state[\"sixes\"])\n", "    \n", "    return {\"bpb\": bpb}"]}, {"cell_type": "code", "execution_count": 23, "id": "ae0ee72e", "metadata": {}, "outputs": [], "source": ["def calculate_boundary_percent(state: BatsmanState) -> BatsmanState:\n", "    boundary_percent = (((state[\"fours\"] * 4) + (state[\"sixes\"] * 6)) / state[\"runs\"]) * 100\n", "    \n", "    return {\"boundary_percent\": boundary_percent}"]}, {"cell_type": "code", "execution_count": 24, "id": "1ad7e0ec", "metadata": {}, "outputs": [], "source": ["def summary(state: BatsmanState) -> BatsmanState:\n", "    summary = f\"Strike Rate - {state['sr']} \\nBalls per boundary - {state['bpb']} \\nBoundary percent - {state['boundary_percent']}\"\n", "    \n", "    return {\"summary\": summary}"]}, {"cell_type": "code", "execution_count": 25, "id": "332d246a", "metadata": {}, "outputs": [], "source": ["graph = StateGraph(BatsmanState)\n", "\n", "graph.add_node(\"calculate_sr\", calculate_sr)\n", "graph.add_node(\"calculate_bpb\", calculate_bpb)\n", "graph.add_node(\"calculate_boundary_percent\", calculate_boundary_percent)\n", "graph.add_node(\"summary\", summary)\n", "\n", "graph.add_edge(START, \"calculate_sr\")\n", "graph.add_edge(START, \"calculate_bpb\")\n", "graph.add_edge(START, \"calculate_boundary_percent\")\n", "\n", "graph.add_edge(\"calculate_sr\", \"summary\")\n", "graph.add_edge(\"calculate_bpb\", \"summary\")\n", "graph.add_edge(\"calculate_boundary_percent\", \"summary\")\n", "\n", "graph.add_edge(\"summary\", END)\n", "\n", "workflow = graph.compile()"]}, {"cell_type": "code", "execution_count": 26, "id": "32cbc9f0", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<langgraph.graph.state.CompiledStateGraph object at 0x7f5149acc640>"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["workflow"]}, {"cell_type": "code", "execution_count": 27, "id": "ce08ec0e", "metadata": {}, "outputs": [], "source": ["initital_state = {\n", "  'runs': 200,\n", "  'balls': 100,\n", "  'fours': 10,\n", "  'sixes': 5\n", "}\n", "\n", "final_state = workflow.invoke(initital_state)"]}, {"cell_type": "code", "execution_count": 28, "id": "fffb50e8", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'runs': 200,\n", " 'balls': 100,\n", " 'fours': 10,\n", " 'sixes': 5,\n", " 'sr': 200.0,\n", " 'bpb': 6.666666666666667,\n", " 'boundary_percent': 35.0,\n", " 'summary': 'Strike Rate - 200.0 \\nBalls per boundary - 6.666666666666667 \\nBoundary percent - 35.0'}"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["final_state"]}, {"cell_type": "code", "execution_count": null, "id": "138de093", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "langgraph-tutorials", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 5}