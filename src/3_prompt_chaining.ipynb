{"cells": [{"cell_type": "code", "execution_count": 58, "id": "3b9f883a", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph, START, END\n", "from langchain_openai import ChatOpenAI\n", "from typing import TypedDict\n", "from dotenv import load_dotenv"]}, {"cell_type": "code", "execution_count": 59, "id": "df80a864", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["load_dotenv()"]}, {"cell_type": "code", "execution_count": 60, "id": "3ecdff5c", "metadata": {}, "outputs": [], "source": ["model = ChatOpenAI()"]}, {"cell_type": "code", "execution_count": 61, "id": "2d10724b", "metadata": {}, "outputs": [], "source": ["class BlogState(TypedDict):\n", "    title: str\n", "    outline: str\n", "    content: str\n", "    rating: int"]}, {"cell_type": "code", "execution_count": 62, "id": "d1b15c6f", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/Programming/langgraph/langgraph-tutorials/.venv/lib/python3.10/site-packages/langchain_openai/chat_models/base.py:1927: UserWarning: Cannot use method='json_schema' with model gpt-3.5-turbo since it doesn't support OpenAI's Structured Output API. You can see supported models here: https://platform.openai.com/docs/guides/structured-outputs#supported-models. To fix this warning, set `method='function_calling'. Overriding to method='function_calling'.\n", "  warnings.warn(\n"]}], "source": ["structured_model = model.with_structured_output(BlogState)"]}, {"cell_type": "code", "execution_count": 63, "id": "4d7ea2d2", "metadata": {}, "outputs": [], "source": ["def create_outline(state: BlogState) -> BlogState:\n", "    prompt = f\"Generate an detailed outline for a blog post about {state['title']}\"\n", "    response = structured_model.invoke(prompt)\n", "    state['outline'] = response['outline']\n", "    return state"]}, {"cell_type": "code", "execution_count": 64, "id": "db7f056b", "metadata": {}, "outputs": [], "source": ["def write_content(state: BlogState) -> BlogState:\n", "  title = state['title']\n", "  outline = state['outline']\n", "  prompt = f\"Write a blog post about {title} using the following outline: {outline}\"\n", "  response = structured_model.invoke(prompt)\n", "  state['content'] = response['content']\n", "  return state"]}, {"cell_type": "code", "execution_count": 65, "id": "5b25c9bf", "metadata": {}, "outputs": [], "source": ["def evaluate_content(state: BlogState) -> BlogState:\n", "  outline = state['outline']\n", "  content = state['content']\n", "  prompt = f\"Based on the outline: {outline} Rate the following blog post on a scale of 1 to 10: {content}\"\n", "  response = structured_model.invoke(prompt)\n", "  state['rating'] = response['rating']\n", "  return state"]}, {"cell_type": "code", "execution_count": 66, "id": "6ee23890", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<langgraph.graph.state.CompiledStateGraph object at 0x7f51b511a440>"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["graph = StateGraph(BlogState)\n", "\n", "graph.add_node(\"create_outline\", create_outline)\n", "graph.add_node(\"write_content\", write_content)\n", "graph.add_node(\"evaluate_content\", evaluate_content)\n", "\n", "graph.add_edge(START, \"create_outline\")\n", "graph.add_edge(\"create_outline\", \"write_content\")\n", "graph.add_edge(\"write_content\", \"evaluate_content\")\n", "graph.add_edge(\"evaluate_content\", END)\n", "\n", "workflow = graph.compile()\n", "workflow\n"]}, {"cell_type": "code", "execution_count": 67, "id": "8133884e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'title': 'AI in Healthcare', 'outline': 'I. Introduction\\nII. The Current State of Healthcare\\nIII. The Benefits of AI in Healthcare\\n    A. Early Detection and Diagnosis\\n    B. Personalized Treatment Plans\\n    C. Streamlined Administrative Processes\\nIV. Challenges and Concerns\\n    A. Data Privacy and Security\\n    B. Integration with Existing Systems\\nV. Future Prospects\\n    A. AI-Powered Medical Research\\n    B. Enhanced Patient Care\\n    C. Ethical Considerations\\nVI. Conclusion', 'content': 'Artificial Intelligence (AI) has revolutionized the healthcare industry in recent years, offering a wide range of benefits and opportunities for improving patient care and outcomes. In this blog post, we will explore the role of AI in healthcare, from early detection and diagnosis to personalized treatment plans and streamlined administrative processes.\\n\\n### I. Introduction\\n\\n### II. The Current State of Healthcare\\n\\n### III. The Benefits of AI in Healthcare\\n\\n#### A. Early Detection and Diagnosis\\n\\n#### B. Personalized Treatment Plans\\n\\n#### C. Streamlined Administrative Processes\\n\\n### IV. Challenges and Concerns\\n\\n#### A. Data Privacy and Security\\n\\n#### B. Integration with Existing Systems\\n\\n### V. Future Prospects\\n\\n#### A. AI-Powered Medical Research\\n\\n#### B. Enhanced Patient Care\\n\\n#### C. Ethical Considerations\\n\\n### VI. Conclusion', 'rating': 9}\n"]}], "source": ["initial_state = {\"title\": \"AI in Healthcare\"}\n", "final_state = workflow.invoke(initial_state)\n", "\n", "print(final_state)"]}, {"cell_type": "code", "execution_count": 68, "id": "6ee9a075", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I. Introduction\n", "II. The Current State of Healthcare\n", "III. The Benefits of AI in Healthcare\n", "    <PERSON><PERSON> Early Detection and Diagnosis\n", "    B. Personalized Treatment Plans\n", "    C. Streamlined Administrative Processes\n", "IV. Challenges and Concerns\n", "    A. Data Privacy and Security\n", "    B. Integration with Existing Systems\n", "V. Future Prospects\n", "    A. AI-Powered Medical Research\n", "    B. Enhanced Patient Care\n", "    C. Ethical Considerations\n", "VI. Conclusion\n"]}], "source": ["print(final_state['outline'])"]}, {"cell_type": "code", "execution_count": 69, "id": "7148b859", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Artificial Intelligence (AI) has revolutionized the healthcare industry in recent years, offering a wide range of benefits and opportunities for improving patient care and outcomes. In this blog post, we will explore the role of AI in healthcare, from early detection and diagnosis to personalized treatment plans and streamlined administrative processes.\n", "\n", "### I. Introduction\n", "\n", "### II. The Current State of Healthcare\n", "\n", "### III. The Benefits of AI in Healthcare\n", "\n", "#### <PERSON>. Early Detection and Diagnosis\n", "\n", "#### B. Personalized Treatment Plans\n", "\n", "#### C. Streamlined Administrative Processes\n", "\n", "### IV. Challenges and Concerns\n", "\n", "#### A. Data Privacy and Security\n", "\n", "#### B. Integration with Existing Systems\n", "\n", "### V. Future Prospects\n", "\n", "#### A. AI-Powered Medical Research\n", "\n", "#### <PERSON>. Enhanced Patient Care\n", "\n", "#### C. Ethical Considerations\n", "\n", "### VI. Conclusion\n"]}], "source": ["print(final_state['content'])"]}, {"cell_type": "code", "execution_count": 70, "id": "9dea148f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["9\n"]}], "source": ["print(final_state['rating'])"]}, {"cell_type": "code", "execution_count": null, "id": "272fa3d4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "langgraph-tutorials", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 5}