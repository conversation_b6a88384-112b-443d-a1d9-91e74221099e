from langgraph.graph import StateGraph, START, END
from langchain_openai import ChatOpenAI
from typing import TypedDict
from dotenv import load_dotenv

load_dotenv()

model = ChatOpenAI()

class BlogState(TypedDict):
    title: str
    outline: str
    content: str
    rating: int

structured_model = model.with_structured_output(BlogState)

def create_outline(state: BlogState) -> BlogState:
    prompt = f"Generate an detailed outline for a blog post about {state['title']}"
    outline = structured_model.invoke(prompt)['outline']
    state['outline'] = outline
    return state

def write_content(state: BlogState) -> BlogState:
  title = state['title']
  outline = state['outline']
  prompt = f"Write a blog post about {title} using the following outline: {outline}"
  content = structured_model.invoke(prompt)['content']
  state['content'] = content
  return state

def evaluate_content(state: BlogState) -> BlogState:
  outline = state['outline']
  content = state['content']
  prompt = f"Based on the outline: {outline} Rate the following blog post on a scale of 1 to 10: {content}"
  rating = int(structured_model.invoke(prompt)['rating'])
  state['rating'] = rating
  return state

graph = StateGraph(BlogState)

graph.add_node("create_outline", create_outline)
graph.add_node("write_content", write_content)
graph.add_node("evaluate_content", evaluate_content)

graph.add_edge(START, "create_outline")
graph.add_edge("create_outline", "write_content")
graph.add_edge("write_content", "evaluate_content")
graph.add_edge("evaluate_content", END)

workflow = graph.compile()
workflow


initial_state = {"title": "AI in Healthcare"}
final_state = workflow.invoke(initial_state)

print(final_state)

print(final_state['outline'])

print(final_state['content'])

