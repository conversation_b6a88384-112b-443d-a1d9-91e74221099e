{"cells": [{"cell_type": "code", "execution_count": 1, "id": "19a003b8", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph, START, END\n", "from typing import TypedDict"]}, {"cell_type": "code", "execution_count": 2, "id": "985ea82a", "metadata": {}, "outputs": [], "source": ["# define state\n", "class BMIState(TypedDict):\n", "\n", "    weight_kg: float\n", "    height_m: float\n", "    bmi: float\n", "    category: str"]}, {"cell_type": "code", "execution_count": 3, "id": "2c76cf7a", "metadata": {}, "outputs": [], "source": ["def calculate_bmi(state: BMIState) -> BMIState:\n", "\n", "    weight = state['weight_kg']\n", "    height = state['height_m']\n", "\n", "    bmi = weight/(height**2)\n", "\n", "    state['bmi'] = round(bmi, 2)\n", "\n", "    return state\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "id": "b223dba6", "metadata": {}, "outputs": [], "source": ["def label_bmi(state: BMIState) -> BMIState:\n", "\n", "    bmi = state['bmi']\n", "\n", "    if bmi < 18.5:\n", "        state[\"category\"] = \"Underweight\"\n", "    elif 18.5 <= bmi < 25:\n", "        state[\"category\"] = \"Normal\"\n", "    elif 25 <= bmi < 30:\n", "        state[\"category\"] = \"Overweight\"\n", "    else:\n", "        state[\"category\"] = \"Obese\"\n", "\n", "    return state\n"]}, {"cell_type": "code", "execution_count": 5, "id": "bba765a7", "metadata": {}, "outputs": [], "source": ["# define your graph\n", "graph = StateGraph(BMIState)\n", "\n", "# add nodes to your graph\n", "graph.add_node('calculate_bmi', calculate_bmi)\n", "graph.add_node('label_bmi', label_bmi)\n", "\n", "# add edges to your graph\n", "graph.add_edge(START, 'calculate_bmi')\n", "graph.add_edge('calculate_bmi', 'label_bmi')\n", "graph.add_edge('label_bmi', END)\n", "\n", "\n", "# compile the graph\n", "workflow = graph.compile()\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "id": "5361cf9b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'weight_kg': 58, 'height_m': 1.74, 'bmi': 19.16, 'category': 'Normal'}\n"]}], "source": ["# execute the graph\n", "intial_state = {'weight_kg':58, 'height_m':1.74}\n", "\n", "final_state = workflow.invoke(intial_state)\n", "\n", "print(final_state)"]}, {"cell_type": "code", "execution_count": 7, "id": "a92d3f59", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<langgraph.graph.state.CompiledStateGraph object at 0x7f9280342ce0>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# from IPython.display import Image\n", "# Image(workflow.get_graph().draw_mermaid_png())\n", "\n", "# instead of doing above\n", "workflow"]}, {"cell_type": "code", "execution_count": null, "id": "f3b753eb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "langgraph-tutorials", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 5}