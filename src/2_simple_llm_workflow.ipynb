{"cells": [{"cell_type": "code", "execution_count": 1, "id": "7458f661", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph, START, END\n", "from langchain_openai import ChatOpenAI\n", "from typing import TypedDict\n", "from dotenv import load_dotenv"]}, {"cell_type": "code", "execution_count": 2, "id": "e22ac4d2", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["load_dotenv()"]}, {"cell_type": "code", "execution_count": 3, "id": "3e26989a", "metadata": {}, "outputs": [], "source": ["model = ChatOpenAI()"]}, {"cell_type": "code", "execution_count": 4, "id": "ae8c8866", "metadata": {}, "outputs": [], "source": ["class LLMState(TypedDict):\n", "  question: str\n", "  answer: str"]}, {"cell_type": "code", "execution_count": 5, "id": "586c7fbe", "metadata": {}, "outputs": [], "source": ["def llm_qa(state: LLMState) -> LLMState:\n", "  question = state[\"question\"]\n", "  prompt = f\"Answer the following question: {question}\"\n", "  answer = model.invoke(prompt).content\n", "  state[\"answer\"] = answer\n", "  return state"]}, {"cell_type": "code", "execution_count": 6, "id": "6b184b22", "metadata": {}, "outputs": [], "source": ["graph = StateGraph(LLMState)\n", "\n", "graph.add_node(\"llm_qa\", llm_qa)\n", "\n", "graph.add_edge(START, \"llm_qa\")\n", "graph.add_edge(\"llm_qa\", END)\n", "\n", "worflow = graph.compile()\n"]}, {"cell_type": "code", "execution_count": 7, "id": "74936787", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'question': 'What is the capital of France?', 'answer': 'The capital of France is Paris.'}\n"]}], "source": ["initial_state = {\"question\": \"What is the capital of France?\"}\n", "\n", "final_state = worflow.invoke(initial_state)\n", "\n", "print(final_state)"]}, {"cell_type": "code", "execution_count": null, "id": "5e625f81", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "langgraph-tutorials", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 5}