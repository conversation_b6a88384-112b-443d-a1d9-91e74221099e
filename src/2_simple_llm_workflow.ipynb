from langgraph.graph import StateGraph, START, END
from langchain_openai import ChatOpenAI
from typing import TypedDict
from dotenv import load_dotenv

load_dotenv()

model = ChatOpenAI()

class LLMState(TypedDict):
  question: str
  answer: str

def llm_qa(state: LLMState) -> LLMState:
  question = state["question"]
  prompt = f"Answer the following question: {question}"
  answer = model.invoke(prompt).content
  state["answer"] = answer
  return state

graph = StateGraph(LLMState)

graph.add_node("llm_qa", llm_qa)

graph.add_edge(START, "llm_qa")
graph.add_edge("llm_qa", END)

worflow = graph.compile()


initial_state = {"question": "What is the capital of France?"}

final_state = worflow.invoke(initial_state)

print(final_state)

