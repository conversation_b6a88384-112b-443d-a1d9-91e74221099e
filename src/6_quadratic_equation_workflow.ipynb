{"cells": [{"cell_type": "code", "execution_count": 29, "id": "35f01e7a", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph, START, END\n", "\n", "from typing import TypedDict, Literal"]}, {"cell_type": "code", "execution_count": 30, "id": "7952dd33", "metadata": {}, "outputs": [], "source": ["class QuadState(TypedDict):\n", "    a: int\n", "    b: int\n", "    c: int\n", "    \n", "    equation: str\n", "    discriminant: float\n", "    result: str"]}, {"cell_type": "code", "execution_count": 31, "id": "18de59eb", "metadata": {}, "outputs": [], "source": ["def show_equation(state: QuadState) -> QuadState:\n", "    b = state[\"b\"] > 0 and f'+{state[\"b\"]}' or f'{state[\"b\"]}'\n", "    c = state[\"c\"] > 0 and f'+{state[\"c\"]}' or f'{state[\"c\"]}'\n", "    equation = f'{state[\"a\"]}x2{b}x{c}'\n", "    return {'equation': equation}\n", "\n", "def calculate_discriminant(state: QuadState) -> QuadState:\n", "    discriminant = state[\"b\"]**2 - (4*state[\"a\"]*state[\"c\"])\n", "    return {'discriminant': discriminant}\n", "\n", "def real_roots(state: QuadState) -> QuadState:\n", "    root1 = (-state[\"b\"] + state[\"discriminant\"]**0.5)/(2*state[\"a\"])\n", "    root2 = (-state[\"b\"] - state[\"discriminant\"]**0.5)/(2*state[\"a\"])\n", "    result = f'The roots are {root1} and {root2}'\n", "    return {'result': result}\n", "\n", "def repeated_roots(state: QuadState) -> QuadState:\n", "    root = (-state[\"b\"])/(2*state[\"a\"])\n", "    result = f'Only repeating root is {root}'\n", "    return {'result': result}\n", "\n", "def no_real_roots(state: QuadState) -> QuadState:\n", "    result = f'No real roots'\n", "    return {'result': result}\n", "\n", "def check_condition(state: QuadState) -> Literal[\"real_roots\", \"repeated_roots\", \"no_real_roots\"]:\n", "    if state['discriminant'] > 0:\n", "        return \"real_roots\"\n", "    elif state['discriminant'] == 0:\n", "        return \"repeated_roots\"\n", "    else:\n", "        return \"no_real_roots\"  "]}, {"cell_type": "code", "execution_count": 32, "id": "14bd0c11", "metadata": {}, "outputs": [], "source": ["graph = StateGraph(QuadState)\n", "\n", "graph.add_node('show_equation', show_equation)\n", "graph.add_node('calculate_discriminant', calculate_discriminant)\n", "graph.add_node('real_roots', real_roots)\n", "graph.add_node('repeated_roots', repeated_roots)\n", "graph.add_node('no_real_roots', no_real_roots)\n", "\n", "graph.add_edge(START, 'show_equation')\n", "graph.add_edge('show_equation', 'calculate_discriminant')\n", "\n", "graph.add_conditional_edges('calculate_discriminant', check_condition)\n", "\n", "graph.add_edge('real_roots', END)\n", "graph.add_edge('repeated_roots', END)\n", "graph.add_edge('no_real_roots', END)\n", "\n", "workflow = graph.compile()\n"]}, {"cell_type": "code", "execution_count": 33, "id": "d4a6b2d6", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<langgraph.graph.state.CompiledStateGraph object at 0x7f0ecaa8cc70>"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["workflow"]}, {"cell_type": "code", "execution_count": 34, "id": "4d39dc57", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'a': 2,\n", " 'b': -4,\n", " 'c': 1,\n", " 'equation': '2x2-4x+1',\n", " 'discriminant': 8,\n", " 'result': 'The roots are 1.7071067811865475 and 0.2928932188134524'}"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["initial_state = {'a': 2, 'b': -4, 'c': 1}\n", "\n", "workflow.invoke(initial_state)"]}, {"cell_type": "code", "execution_count": null, "id": "c7c90b6e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "langgraph-tutorials", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 5}