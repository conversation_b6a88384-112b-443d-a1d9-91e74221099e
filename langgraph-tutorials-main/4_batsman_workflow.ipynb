{"cells": [{"cell_type": "code", "execution_count": 10, "id": "6ac3cad9", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph, START, END\n", "from typing import TypedDict"]}, {"cell_type": "code", "execution_count": 8, "id": "a822950e", "metadata": {}, "outputs": [], "source": ["class BatsmanState(TypedDict):\n", "\n", "    runs: int\n", "    balls: int\n", "    fours: int\n", "    sixes: int\n", "\n", "    sr: float\n", "    bpb: float\n", "    boundary_percent: float\n", "    summary: str"]}, {"cell_type": "code", "execution_count": 38, "id": "1954ff12", "metadata": {}, "outputs": [], "source": ["def calculate_sr(state: BatsmanState):\n", "\n", "    sr = (state['runs']/state['balls'])*100\n", "    \n", "    return {'sr': sr}"]}, {"cell_type": "code", "execution_count": 32, "id": "e4689c81", "metadata": {}, "outputs": [], "source": ["def calculate_bpb(state: BatsmanState):\n", "\n", "    bpb = state['balls']/(state['fours'] + state['sixes'])\n", "\n", "    return {'bpb': bpb}"]}, {"cell_type": "code", "execution_count": 33, "id": "2f0686b2", "metadata": {}, "outputs": [], "source": ["def calculate_boundary_percent(state: BatsmanState):\n", "\n", "    boundary_percent = (((state['fours'] * 4) + (state['sixes'] * 6))/state['runs'])*100\n", "\n", "    return {'boundary_percent': boundary_percent}"]}, {"cell_type": "code", "execution_count": 34, "id": "9d8fdd9c", "metadata": {}, "outputs": [], "source": ["def summary(state: BatsmanState):\n", "\n", "    summary = f\"\"\"\n", "Strike Rate - {state['sr']} \\n\n", "Balls per boundary - {state['bpb']} \\n\n", "Boundary percent - {state['boundary_percent']}\n", "\"\"\"\n", "    \n", "    return {'summary': summary}"]}, {"cell_type": "code", "execution_count": 40, "id": "ba29814e", "metadata": {}, "outputs": [], "source": ["graph = StateGraph(BatsmanState)\n", "\n", "graph.add_node('calculate_sr', calculate_sr)\n", "graph.add_node('calculate_bpb', calculate_bpb)\n", "graph.add_node('calculate_boundary_percent', calculate_boundary_percent)\n", "graph.add_node('summary', summary)\n", "\n", "# edges\n", "\n", "graph.add_edge(START, 'calculate_sr')\n", "graph.add_edge(START, 'calculate_bpb')\n", "graph.add_edge(START, 'calculate_boundary_percent')\n", "\n", "graph.add_edge('calculate_sr', 'summary')\n", "graph.add_edge('calculate_bpb', 'summary')\n", "graph.add_edge('calculate_boundary_percent', 'summary')\n", "\n", "graph.add_edge('summary', END)\n", "\n", "workflow = graph.compile()\n"]}, {"cell_type": "code", "execution_count": 41, "id": "3a86de8a", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<langgraph.graph.state.CompiledStateGraph object at 0x0000021BB070DB90>"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["workflow"]}, {"cell_type": "code", "execution_count": 42, "id": "64d43eec", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'runs': 100,\n", " 'balls': 50,\n", " 'fours': 6,\n", " 'sixes': 4,\n", " 'sr': 200.0,\n", " 'bpb': 5.0,\n", " 'boundary_percent': 48.0,\n", " 'summary': '\\nStrike Rate - 200.0 \\n\\nBalls per boundary - 5.0 \\n\\nBoundary percent - 48.0\\n'}"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["intial_state = {\n", "    'runs': 100,\n", "    'balls': 50,\n", "    'fours': 6,\n", "    'sixes': 4\n", "}\n", "\n", "workflow.invoke(intial_state)"]}, {"cell_type": "code", "execution_count": null, "id": "4a282b57", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "myenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 5}