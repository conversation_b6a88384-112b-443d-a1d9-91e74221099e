{"cells": [{"cell_type": "code", "execution_count": 2, "id": "0237c339", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph, START, END\n", "from langchain_openai import ChatOpenAI\n", "from typing import TypedDict, Literal\n", "from dotenv import load_dotenv\n", "from pydantic import BaseModel, Field"]}, {"cell_type": "code", "execution_count": 3, "id": "cfd66844", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["load_dotenv()"]}, {"cell_type": "code", "execution_count": 4, "id": "726606b0", "metadata": {}, "outputs": [], "source": ["model = ChatOpenAI(model='gpt-4o-mini')"]}, {"cell_type": "code", "execution_count": null, "id": "3d906b63", "metadata": {}, "outputs": [], "source": ["class SentimentSchema(BaseModel):\n", "\n", "    sentiment: Literal[\"positive\", \"negative\"] = Field(description='Sentiment of the review')"]}, {"cell_type": "code", "execution_count": 18, "id": "5432864c", "metadata": {}, "outputs": [], "source": ["class DiagnosisSchema(BaseModel):\n", "    issue_type: Literal[\"UX\", \"Performance\", \"Bug\", \"Support\", \"Other\"] = Field(description='The category of issue mentioned in the review')\n", "    tone: Literal[\"angry\", \"frustrated\", \"disappointed\", \"calm\"] = Field(description='The emotional tone expressed by the user')\n", "    urgency: Literal[\"low\", \"medium\", \"high\"] = Field(description='How urgent or critical the issue appears to be')\n"]}, {"cell_type": "code", "execution_count": 19, "id": "4a502f1a", "metadata": {}, "outputs": [], "source": ["structured_model = model.with_structured_output(SentimentSchema)\n", "structured_model2 = model.with_structured_output(DiagnosisSchema)"]}, {"cell_type": "code", "execution_count": 9, "id": "611817cf", "metadata": {}, "outputs": [{"data": {"text/plain": ["'positive'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["prompt = 'What is the sentiment of the following review - The software too good'\n", "structured_model.invoke(prompt).sentiment"]}, {"cell_type": "code", "execution_count": 10, "id": "741ee19e", "metadata": {}, "outputs": [], "source": ["class ReviewState(TypedDict):\n", "\n", "    review: str\n", "    sentiment: Literal[\"positive\", \"negative\"]\n", "    diagnosis: dict\n", "    response: str"]}, {"cell_type": "code", "execution_count": 20, "id": "4fec449b", "metadata": {}, "outputs": [], "source": ["def find_sentiment(state: ReviewState):\n", "\n", "    prompt = f'For the following review find out the sentiment \\n {state[\"review\"]}'\n", "    sentiment = structured_model.invoke(prompt).sentiment\n", "\n", "    return {'sentiment': sentiment}\n", "\n", "def check_sentiment(state: ReviewState) -> Literal[\"positive_response\", \"run_diagnosis\"]:\n", "\n", "    if state['sentiment'] == 'positive':\n", "        return 'positive_response'\n", "    else:\n", "        return 'run_diagnosis'\n", "    \n", "def positive_response(state: ReviewState):\n", "\n", "    prompt = f\"\"\"Write a warm thank-you message in response to this review:\n", "    \\n\\n\\\"{state['review']}\\\"\\n\n", "Also, kindly ask the user to leave feedback on our website.\"\"\"\n", "    \n", "    response = model.invoke(prompt).content\n", "\n", "    return {'response': response}\n", "\n", "def run_diagnosis(state: ReviewState):\n", "\n", "    prompt = f\"\"\"Diagnose this negative review:\\n\\n{state['review']}\\n\"\n", "    \"Return issue_type, tone, and urgency.\n", "\"\"\"\n", "    response = structured_model2.invoke(prompt)\n", "\n", "    return {'diagnosis': response.model_dump()}\n", "\n", "def negative_response(state: ReviewState):\n", "\n", "    diagnosis = state['diagnosis']\n", "\n", "    prompt = f\"\"\"You are a support assistant.\n", "The user had a '{diagnosis['issue_type']}' issue, sounded '{diagnosis['tone']}', and marked urgency as '{diagnosis['urgency']}'.\n", "Write an empathetic, helpful resolution message.\n", "\"\"\"\n", "    response = model.invoke(prompt).content\n", "\n", "    return {'response': response}\n", "    "]}, {"cell_type": "code", "execution_count": 21, "id": "4fc9f841", "metadata": {}, "outputs": [], "source": ["graph = StateGraph(ReviewState)\n", "\n", "graph.add_node('find_sentiment', find_sentiment)\n", "graph.add_node('positive_response', positive_response)\n", "graph.add_node('run_diagnosis', run_diagnosis)\n", "graph.add_node('negative_response', negative_response)\n", "\n", "graph.add_edge(START, 'find_sentiment')\n", "\n", "graph.add_conditional_edges('find_sentiment', check_sentiment)\n", "\n", "graph.add_edge('positive_response', END)\n", "\n", "graph.add_edge('run_diagnosis', 'negative_response')\n", "graph.add_edge('negative_response', END)\n", "\n", "workflow = graph.compile()"]}, {"cell_type": "code", "execution_count": 22, "id": "828f2235", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<langgraph.graph.state.CompiledStateGraph object at 0x000001F163E436D0>"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["workflow"]}, {"cell_type": "code", "execution_count": 24, "id": "00577fa6", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'review': 'I’ve been trying to log in for over an hour now, and the app keeps freezing on the authentication screen. I even tried reinstalling it, but no luck. This kind of bug is unacceptable, especially when it affects basic functionality.',\n", " 'sentiment': 'negative',\n", " 'diagnosis': {'issue_type': 'Bug', 'tone': 'frustrated', 'urgency': 'high'},\n", " 'response': \"Subject: We're Here to Help You with the Bug Issue\\n\\nHi [User's Name],\\n\\nThank you for reaching out and bringing this issue to our attention. I understand how frustrating it can be to deal with a bug, especially when it feels urgent. I'm here to help you resolve this as quickly as possible.\\n\\nCould you please provide me with some additional details about the bug you’re experiencing? Specifically, it would be helpful to know:\\n\\n1. A brief description of the issue.\\n2. The steps you took leading up to the bug.\\n3. Any error messages you might have seen.\\n\\nOnce I have this information, I’ll do my best to get you a solution or workaround promptly.\\n\\nThank you for your patience, and I look forward to assisting you!\\n\\nBest,  \\n[Your Name]  \\n[Your Position]  \\n[Your Contact Information]  \"}"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["intial_state={\n", "    'review': \"I’ve been trying to log in for over an hour now, and the app keeps freezing on the authentication screen. I even tried reinstalling it, but no luck. This kind of bug is unacceptable, especially when it affects basic functionality.\"\n", "}\n", "workflow.invoke(intial_state)"]}, {"cell_type": "code", "execution_count": null, "id": "cc9ad387", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "myenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 5}