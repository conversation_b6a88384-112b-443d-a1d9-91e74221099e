{"cells": [{"cell_type": "code", "execution_count": 1, "id": "398994d2", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph, START, END\n", "from langchain_openai import ChatOpenAI\n", "from typing import TypedDict\n", "from dotenv import load_dotenv"]}, {"cell_type": "code", "execution_count": 2, "id": "4abe99d6", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["load_dotenv()"]}, {"cell_type": "code", "execution_count": 3, "id": "3d732ea4", "metadata": {}, "outputs": [], "source": ["model = ChatOpenAI()"]}, {"cell_type": "code", "execution_count": 4, "id": "595d1707", "metadata": {}, "outputs": [], "source": ["# create a state\n", "\n", "class LLMState(TypedDict):\n", "\n", "    question: str\n", "    answer: str"]}, {"cell_type": "code", "execution_count": 5, "id": "6f48a04d", "metadata": {}, "outputs": [], "source": ["def llm_qa(state: LLMState) -> LLMState:\n", "\n", "    # extract the question from state\n", "    question = state['question']\n", "\n", "    # form a prompt\n", "    prompt = f'Answer the following question {question}'\n", "\n", "    # ask that question to the LLM\n", "    answer = model.invoke(prompt).content\n", "\n", "    # update the answer in the state\n", "    state['answer'] = answer\n", "\n", "    return state"]}, {"cell_type": "code", "execution_count": 6, "id": "0cd56cd3", "metadata": {}, "outputs": [], "source": ["# create our graph\n", "\n", "graph = StateGraph(LLMState)\n", "\n", "# add nodes\n", "graph.add_node('llm_qa', llm_qa)\n", "\n", "# add edges\n", "graph.add_edge(START, 'llm_qa')\n", "graph.add_edge('llm_qa', END)\n", "\n", "# compile\n", "workflow = graph.compile()"]}, {"cell_type": "code", "execution_count": 7, "id": "11542604", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The average distance from the Moon to Earth is about 384,400 kilometers (238,855 miles).\n"]}], "source": ["# execute\n", "\n", "intial_state = {'question': 'How far is moon from the earth?'}\n", "\n", "final_state = workflow.invoke(intial_state)\n", "\n", "print(final_state['answer'])\n", "\n"]}, {"cell_type": "code", "execution_count": 13, "id": "9d1f9814", "metadata": {}, "outputs": [{"data": {"text/plain": ["'The average distance from the Earth to the Moon is about 384,400 kilometers (238,855 miles).'"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["model.invoke('How far is moon from the earth?').content"]}, {"cell_type": "code", "execution_count": null, "id": "7d170d7d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "langgraph-tutorials", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 5}