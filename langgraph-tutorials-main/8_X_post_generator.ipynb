{"cells": [{"cell_type": "code", "execution_count": 80, "id": "c1169591", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph,START, END\n", "from typing import TypedDict, Literal, Annotated\n", "from langchain_openai import ChatOpenAI\n", "from langchain_core.messages import SystemMessage, HumanMessage\n", "import operator"]}, {"cell_type": "code", "execution_count": 81, "id": "1b4151ce", "metadata": {}, "outputs": [], "source": ["generator_llm = ChatOpenAI(model='gpt-4o-mini')\n", "evaluator_llm = ChatOpenAI(model='gpt-4o-mini')\n", "optimizer_llm = ChatOpenAI(model='gpt-4o-mini')"]}, {"cell_type": "code", "execution_count": 82, "id": "a1b212e6", "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel, Field\n", "\n", "class TweetEvaluation(BaseModel):\n", "    evaluation: Literal[\"approved\", \"needs_improvement\"] = Field(..., description=\"Final evaluation result.\")\n", "    feedback: str = Field(..., description=\"feedback for the tweet.\")\n"]}, {"cell_type": "code", "execution_count": 83, "id": "cc98e866", "metadata": {}, "outputs": [], "source": ["structured_evaluator_llm = evaluator_llm.with_structured_output(TweetEvaluation)"]}, {"cell_type": "code", "execution_count": 84, "id": "c6de039a", "metadata": {}, "outputs": [], "source": ["# state\n", "class TweetState(TypedDict):\n", "\n", "    topic: str\n", "    tweet: str\n", "    evaluation: Literal[\"approved\", \"needs_improvement\"]\n", "    feedback: str\n", "    iteration: int\n", "    max_iteration: int\n", "\n", "    tweet_history: Annotated[list[str], operator.add]\n", "    feedback_history: Annotated[list[str], operator.add]"]}, {"cell_type": "code", "execution_count": 85, "id": "216a94a1", "metadata": {}, "outputs": [], "source": ["def generate_tweet(state: TweetState):\n", "\n", "    # prompt\n", "    messages = [\n", "        SystemMessage(content=\"You are a funny and clever Twitter/X influencer.\"),\n", "        HumanMessage(content=f\"\"\"\n", "Write a short, original, and hilarious tweet on the topic: \"{state['topic']}\".\n", "\n", "Rules:\n", "- Do NOT use question-answer format.\n", "- Max 280 characters.\n", "- Use observational humor, irony, sarcasm, or cultural references.\n", "- Think in meme logic, punchlines, or relatable takes.\n", "- Use simple, day to day english\n", "\"\"\")\n", "    ]\n", "\n", "    # send generator_llm\n", "    response = generator_llm.invoke(messages).content\n", "\n", "    # return response\n", "    return {'tweet': response, 'tweet_history': [response]}"]}, {"cell_type": "code", "execution_count": 86, "id": "5a92e27e", "metadata": {}, "outputs": [], "source": ["def evaluate_tweet(state: TweetState):\n", "\n", "    # prompt\n", "    messages = [\n", "    SystemMessage(content=\"You are a ruthless, no-laugh-given Twitter critic. You evaluate tweets based on humor, originality, virality, and tweet format.\"),\n", "    HumanMessage(content=f\"\"\"\n", "Evaluate the following tweet:\n", "\n", "Tweet: \"{state['tweet']}\"\n", "\n", "Use the criteria below to evaluate the tweet:\n", "\n", "1. Originality – Is this fresh, or have you seen it a hundred times before?  \n", "2. <PERSON><PERSON> – Did it genuinely make you smile, laugh, or chuckle?  \n", "3. Punchiness – Is it short, sharp, and scroll-stopping?  \n", "4. Virality Potential – Would people retweet or share it?  \n", "5. Format – Is it a well-formed tweet (not a setup-punchline joke, not a Q&A joke, and under 280 characters)?\n", "\n", "Auto-reject if:\n", "- It's written in question-answer format (e.g., \"Why did...\" or \"What happens when...\")\n", "- It exceeds 280 characters\n", "- It reads like a traditional setup-punchline joke\n", "- Dont end with generic, throwaway, or deflating lines that weaken the humor (e.g., “Masterpieces of the auntie-uncle universe” or vague summaries)\n", "\n", "### Respond ONLY in structured format:\n", "- evaluation: \"approved\" or \"needs_improvement\"  \n", "- feedback: One paragraph explaining the strengths and weaknesses \n", "\"\"\")\n", "]\n", "\n", "    response = structured_evaluator_llm.invoke(messages)\n", "\n", "    return {'evaluation':response.evaluation, 'feedback': response.feedback, 'feedback_history': [response.feedback]}"]}, {"cell_type": "code", "execution_count": 87, "id": "6670db83", "metadata": {}, "outputs": [], "source": ["def optimize_tweet(state: TweetState):\n", "\n", "    messages = [\n", "        SystemMessage(content=\"You punch up tweets for virality and humor based on given feedback.\"),\n", "        HumanMessage(content=f\"\"\"\n", "Improve the tweet based on this feedback:\n", "\"{state['feedback']}\"\n", "\n", "Topic: \"{state['topic']}\"\n", "Original Tweet:\n", "{state['tweet']}\n", "\n", "Re-write it as a short, viral-worthy tweet. Avoid Q&A style and stay under 280 characters.\n", "\"\"\")\n", "    ]\n", "\n", "    response = optimizer_llm.invoke(messages).content\n", "    iteration = state['iteration'] + 1\n", "\n", "    return {'tweet': response, 'iteration': iteration, 'tweet_history': [response]}"]}, {"cell_type": "code", "execution_count": 88, "id": "e0d487a1", "metadata": {}, "outputs": [], "source": ["def route_evaluation(state: TweetState):\n", "\n", "    if state['evaluation'] == 'approved' or state['iteration'] >= state['max_iteration']:\n", "        return 'approved'\n", "    else:\n", "        return 'needs_improvement'"]}, {"cell_type": "code", "execution_count": 89, "id": "91e30877", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<langgraph.graph.state.CompiledStateGraph object at 0x0000015352442690>"]}, "execution_count": 89, "metadata": {}, "output_type": "execute_result"}], "source": ["graph = StateGraph(TweetState)\n", "\n", "graph.add_node('generate', generate_tweet)\n", "graph.add_node('evaluate', evaluate_tweet)\n", "graph.add_node('optimize', optimize_tweet)\n", "\n", "graph.add_edge(START, 'generate')\n", "graph.add_edge('generate', 'evaluate')\n", "\n", "graph.add_conditional_edges('evaluate', route_evaluation, {'approved': END, 'needs_improvement': 'optimize'})\n", "graph.add_edge('optimize', 'evaluate')\n", "\n", "workflow = graph.compile()\n", "\n", "workflow\n", "\n"]}, {"cell_type": "code", "execution_count": 109, "id": "f98ff23c", "metadata": {}, "outputs": [], "source": ["initial_state = {\n", "    \"topic\": \"srhberhb\",\n", "    \"iteration\": 1,\n", "    \"max_iteration\": 5\n", "}\n", "result = workflow.invoke(initial_state)"]}, {"cell_type": "code", "execution_count": 107, "id": "52bdf78c", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'topic': 'srhberhb',\n", " 'tweet': 'Just discovered that \"srhberhb\" is the new secret code for understanding adulting. It translates to: “Stress, Responsibilities, Hopes, Breakdowns, Emotions, Regrets, Heartbeats, and Breaths.” Pretty much sums up my Monday vibes. 😂',\n", " 'evaluation': 'approved',\n", " 'feedback': \"This tweet scores well on originality, presenting a fresh take on the struggles of adulting with a clever blend of relatable themes encapsulated in the nonsensical code. The humor is lighthearted and relatable, likely to resonate with many readers as they identify with the described 'Monday vibes.' The punchiness is decent, maintaining a manageable length while conveying a full thought, which aids its potential to be shared or retweeted. The format is appropriate for Twitter, adhering to character limits and avoiding traditional setups. Overall, it effectively combines wit with relatability, making it a solid tweet.\",\n", " 'iteration': 1,\n", " 'max_iteration': 5,\n", " 'tweet_history': ['Just discovered that \"srhberhb\" is the new secret code for understanding adulting. It translates to: “Stress, Responsibilities, Hopes, Breakdowns, Emotions, Regrets, Heartbeats, and Breaths.” Pretty much sums up my Monday vibes. 😂'],\n", " 'feedback_history': [\"This tweet scores well on originality, presenting a fresh take on the struggles of adulting with a clever blend of relatable themes encapsulated in the nonsensical code. The humor is lighthearted and relatable, likely to resonate with many readers as they identify with the described 'Monday vibes.' The punchiness is decent, maintaining a manageable length while conveying a full thought, which aids its potential to be shared or retweeted. The format is appropriate for Twitter, adhering to character limits and avoiding traditional setups. Overall, it effectively combines wit with relatability, making it a solid tweet.\"]}"]}, "execution_count": 107, "metadata": {}, "output_type": "execute_result"}], "source": ["result"]}, {"cell_type": "code", "execution_count": 108, "id": "6945c06d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Just discovered that \"srhberhb\" is the new secret code for understanding adulting. It translates to: “Stress, Responsibilities, Hopes, Breakdowns, Emotions, Regrets, Heartbeats, and Breaths.” Pretty much sums up my Monday vibes. 😂\n"]}], "source": ["for tweet in result['tweet_history']:\n", "    print(tweet)"]}, {"cell_type": "code", "execution_count": null, "id": "d63cf13a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "myenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 5}