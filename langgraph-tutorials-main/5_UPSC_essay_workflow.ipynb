{"cells": [{"cell_type": "code", "execution_count": 15, "id": "6adeeb78", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph, START, END\n", "from langchain_openai import ChatOpenAI\n", "from dotenv import load_dotenv\n", "from typing import TypedDict, Annotated\n", "from pydantic import BaseModel, Field\n", "import operator"]}, {"cell_type": "code", "execution_count": 3, "id": "39ebcf04", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["load_dotenv()"]}, {"cell_type": "code", "execution_count": 5, "id": "b437e659", "metadata": {}, "outputs": [], "source": ["model = ChatOpenAI(model='gpt-4o-mini')"]}, {"cell_type": "code", "execution_count": 6, "id": "13a12851", "metadata": {}, "outputs": [], "source": ["class EvaluationSchema(BaseModel):\n", "\n", "    feedback: str = Field(description='Detailed feedbackfor the essay')\n", "    score: int = Field(description='Score out of 10', ge=0, le=10)"]}, {"cell_type": "code", "execution_count": 7, "id": "b685f826", "metadata": {}, "outputs": [], "source": ["structured_model = model.with_structured_output(EvaluationSchema)"]}, {"cell_type": "code", "execution_count": 8, "id": "e5a52026", "metadata": {}, "outputs": [], "source": ["essay = \"\"\"India in the Age of AI\n", "As the world enters a transformative era defined by artificial intelligence (AI), India stands at a critical juncture — one where it can either emerge as a global leader in AI innovation or risk falling behind in the technology race. The age of AI brings with it immense promise as well as unprecedented challenges, and how India navigates this landscape will shape its socio-economic and geopolitical future.\n", "\n", "India's strengths in the AI domain are rooted in its vast pool of skilled engineers, a thriving IT industry, and a growing startup ecosystem. With over 5 million STEM graduates annually and a burgeoning base of AI researchers, India possesses the intellectual capital required to build cutting-edge AI systems. Institutions like IITs, IIITs, and IISc have begun fostering AI research, while private players such as TCS, Infosys, and Wipro are integrating AI into their global services. In 2020, the government launched the National AI Strategy (AI for All) with a focus on inclusive growth, aiming to leverage AI in healthcare, agriculture, education, and smart mobility.\n", "\n", "One of the most promising applications of AI in India lies in agriculture, where predictive analytics can guide farmers on optimal sowing times, weather forecasts, and pest control. In healthcare, AI-powered diagnostics can help address India’s doctor-patient ratio crisis, particularly in rural areas. Educational platforms are increasingly using AI to personalize learning paths, while smart governance tools are helping improve public service delivery and fraud detection.\n", "\n", "However, the path to AI-led growth is riddled with challenges. Chief among them is the digital divide. While metropolitan cities may embrace AI-driven solutions, rural India continues to struggle with basic internet access and digital literacy. The risk of job displacement due to automation also looms large, especially for low-skilled workers. Without effective skilling and re-skilling programs, AI could exacerbate existing socio-economic inequalities.\n", "\n", "Another pressing concern is data privacy and ethics. As AI systems rely heavily on vast datasets, ensuring that personal data is used transparently and responsibly becomes vital. India is still shaping its data protection laws, and in the absence of a strong regulatory framework, AI systems may risk misuse or bias.\n", "\n", "To harness AI responsibly, India must adopt a multi-stakeholder approach involving the government, academia, industry, and civil society. Policies should promote open datasets, encourage responsible innovation, and ensure ethical AI practices. There is also a need for international collaboration, particularly with countries leading in AI research, to gain strategic advantage and ensure interoperability in global systems.\n", "\n", "India’s demographic dividend, when paired with responsible AI adoption, can unlock massive economic growth, improve governance, and uplift marginalized communities. But this vision will only materialize if AI is seen not merely as a tool for automation, but as an enabler of human-centered development.\n", "\n", "In conclusion, India in the age of AI is a story in the making — one of opportunity, responsibility, and transformation. The decisions we make today will not just determine India’s AI trajectory, but also its future as an inclusive, equitable, and innovation-driven society.\"\"\""]}, {"cell_type": "code", "execution_count": 13, "id": "81a54345", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"The essay presents a well-structured and comprehensive discussion of India's potential in the age of artificial intelligence. The language used is formal and appropriate for the topic, with a clear flow of ideas and effective transitions between points. However, there are minor issues that can be improved. There is a slight repetitive use of similar phrases that could be varied for better engagement. Additionally, some complex sentences could be simplified to enhance readability. There are also some statments that could benefit from citing specific examples or data to strengthen arguments, particularly in the section discussing challenges and opportunities. Overall, the essay effectively conveys its message but could use slight refinement for clarity and engagement.\""]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["prompt = f'Evaluate the language quality of the following essay and provide a feedback and assign a score out of 10 \\n {essay}'\n", "structured_model.invoke(prompt).feedback"]}, {"cell_type": "code", "execution_count": 17, "id": "ce0010cd", "metadata": {}, "outputs": [], "source": ["class UPSCState(TypedDict):\n", "\n", "    essay: str\n", "    language_feedback: str\n", "    analysis_feedback: str\n", "    clarity_feedback: str\n", "    overall_feedback: str\n", "    individual_scores: Annotated[list[int], operator.add]\n", "    avg_score: float"]}, {"cell_type": "code", "execution_count": 23, "id": "021f54c3", "metadata": {}, "outputs": [], "source": ["def evaluate_language(state: UPSCState):\n", "\n", "    prompt = f'Evaluate the language quality of the following essay and provide a feedback and assign a score out of 10 \\n {state[\"essay\"]}'\n", "    output = structured_model.invoke(prompt)\n", "\n", "    return {'language_feedback': output.feedback, 'individual_scores': [output.score]}"]}, {"cell_type": "code", "execution_count": 24, "id": "4eade564", "metadata": {}, "outputs": [], "source": ["def evaluate_analysis(state: UPSCState):\n", "\n", "    prompt = f'Evaluate the depth of analysis of the following essay and provide a feedback and assign a score out of 10 \\n {state[\"essay\"]}'\n", "    output = structured_model.invoke(prompt)\n", "\n", "    return {'analysis_feedback': output.feedback, 'individual_scores': [output.score]}"]}, {"cell_type": "code", "execution_count": 25, "id": "ba9dc97b", "metadata": {}, "outputs": [], "source": ["def evaluate_thought(state: UPSCState):\n", "\n", "    prompt = f'Evaluate the clarity of thought of the following essay and provide a feedback and assign a score out of 10 \\n {state[\"essay\"]}'\n", "    output = structured_model.invoke(prompt)\n", "\n", "    return {'clarity_feedback': output.feedback, 'individual_scores': [output.score]}"]}, {"cell_type": "code", "execution_count": 26, "id": "147996d8", "metadata": {}, "outputs": [], "source": ["def final_evaluation(state: UPSCState):\n", "\n", "    # summary feedback\n", "    prompt = f'Based on the following feedbacks create a summarized feedback \\n language feedback - {state[\"language_feedback\"]} \\n depth of analysis feedback - {state[\"analysis_feedback\"]} \\n clarity of thought feedback - {state[\"clarity_feedback\"]}'\n", "    overall_feedback = model.invoke(prompt).content\n", "\n", "    # avg calculate\n", "    avg_score = sum(state['individual_scores'])/len(state['individual_scores'])\n", "\n", "    return {'overall_feedback': overall_feedback, 'avg_score': avg_score}\n", "    "]}, {"cell_type": "code", "execution_count": 28, "id": "b288b925", "metadata": {}, "outputs": [], "source": ["graph = StateGraph(UPSCState)\n", "\n", "graph.add_node('evaluate_language', evaluate_language)\n", "graph.add_node('evaluate_analysis', evaluate_analysis)\n", "graph.add_node('evaluate_thought', evaluate_thought)\n", "graph.add_node('final_evaluation', final_evaluation)\n", "\n", "# edges\n", "graph.add_edge(START, 'evaluate_language')\n", "graph.add_edge(START, 'evaluate_analysis')\n", "graph.add_edge(START, 'evaluate_thought')\n", "\n", "graph.add_edge('evaluate_language', 'final_evaluation')\n", "graph.add_edge('evaluate_analysis', 'final_evaluation')\n", "graph.add_edge('evaluate_thought', 'final_evaluation')\n", "\n", "graph.add_edge('final_evaluation', END)\n", "\n", "workflow = graph.compile()\n", "\n"]}, {"cell_type": "code", "execution_count": 29, "id": "b778ae72", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<langgraph.graph.state.CompiledStateGraph object at 0x00000200793BA6D0>"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["workflow"]}, {"cell_type": "code", "execution_count": 31, "id": "d7eef78e", "metadata": {}, "outputs": [], "source": ["essay2 = \"\"\"India and AI Time\n", "\n", "Now world change very fast because new tech call Artificial Intel… something (AI). India also want become big in this AI thing. If work hard, India can go top. But if no careful, India go back.\n", "\n", "India have many good. We have smart student, many engine-ear, and good IT peoples. Big company like TCS, Infosys, Wipro already use AI. Government also do program “AI for All”. It want AI in farm, doctor place, school and transport.\n", "\n", "In farm, AI help farmer know when to put seed, when rain come, how stop bug. In health, AI help doctor see sick early. In school, AI help student learn good. Government office use AI to find bad people and work fast.\n", "\n", "But problem come also. First is many villager no have phone or internet. So AI not help them. Second, many people lose job because AI and machine do work. Poor people get more bad.\n", "\n", "One more big problem is privacy. AI need big big data. Who take care? India still make data rule. If no strong rule, AI do bad.\n", "\n", "India must all people together – govern, school, company and normal people. We teach AI and make sure AI not bad. Also talk to other country and learn from them.\n", "\n", "If India use AI good way, we become strong, help poor and make better life. But if only rich use AI, and poor no get, then big bad thing happen.\n", "\n", "So, in short, AI time in India have many hope and many danger. We must go right road. AI must help all people, not only some. Then India grow big and world say \"good job India\".\"\"\""]}, {"cell_type": "code", "execution_count": 32, "id": "a4c8cfc6", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'essay': 'India and AI Time\\n\\nNow world change very fast because new tech call Artificial Intel… something (AI). India also want become big in this AI thing. If work hard, India can go top. But if no careful, India go back.\\n\\nIndia have many good. We have smart student, many engine-ear, and good IT peoples. Big company like TCS, Infosys, Wipro already use AI. Government also do program “AI for All”. It want AI in farm, doctor place, school and transport.\\n\\nIn farm, AI help farmer know when to put seed, when rain come, how stop bug. In health, AI help doctor see sick early. In school, AI help student learn good. Government office use AI to find bad people and work fast.\\n\\nBut problem come also. First is many villager no have phone or internet. So AI not help them. Second, many people lose job because AI and machine do work. Poor people get more bad.\\n\\nOne more big problem is privacy. AI need big big data. Who take care? India still make data rule. If no strong rule, AI do bad.\\n\\nIndia must all people together – govern, school, company and normal people. We teach AI and make sure AI not bad. Also talk to other country and learn from them.\\n\\nIf India use AI good way, we become strong, help poor and make better life. But if only rich use AI, and poor no get, then big bad thing happen.\\n\\nSo, in short, AI time in India have many hope and many danger. We must go right road. AI must help all people, not only some. Then India grow big and world say \"good job India\".',\n", " 'language_feedback': \"The essay attempts to address a significant topic, discussing India's potential in the AI sector alongside its challenges. However, the language quality is quite poor and exhibits a range of grammatical and structural issues. For instance, phrases like 'new tech call Artificial Intel… something (AI)' are unclear and unprofessional. The use of simple language is appropriate for some contexts, but here it seems overly simplistic and at times incorrect; for example, 'India also want become big' instead of ‘India also wants to become big.’ There are frequent subject-verb agreement errors, incorrect word choices ('engine-ear' instead of 'engineers'), and awkward phrasing throughout. The essay would benefit greatly from a more structured approach, using paragraphs effectively to organize ideas logically, and presenting a clearer introduction and conclusion.\",\n", " 'analysis_feedback': \"The essay presents a basic overview of the potential benefits and challenges of AI in India and attempts to cover various aspects such as agriculture, healthcare, education, and privacy concerns. However, the depth of analysis is shallow, with several points introduced but not elaborated upon. The argument lacks supporting evidence, and the language is informal and contains grammatical errors, which detracts from clarity and professionalism. There is a need for more critical thinking, specific examples, and a clearer structure to enhance the essay's coherence and persuasiveness. Overall, while the essay touches on important topics, it fails to provide a robust analysis or nuanced understanding of the issues discussed.\",\n", " 'clarity_feedback': 'The essay presents a clear and simple overview of the potential benefits and issues surrounding AI in India. However, the clarity of thought is hindered by several grammar issues, awkward phrasing, and a lack of structure that limits the overall coherence. The argument is somewhat disorganized, moving from benefits to problems without clear transitions or a logical flow. For instance, while the intention to discuss various sectors where AI can be beneficial is evident, the way it is presented feels fragmented. Additionally, the use of overly simplistic language and repetitive statements detracts from the sophistication expected in an academic essay. It would benefit from a more structured approach—perhaps starting with a thesis statement, followed by distinct sections for benefits and challenges, and culminating in a strong conclusion. Overall, while the essay communicates important ideas, the effectiveness is undermined by its lack of clarity and polish.',\n", " 'overall_feedback': '**Summarized Feedback:**\\n\\nThe essay addresses an important topic regarding India\\'s potential in the AI sector, but it suffers from significant language and structural issues. The overall quality is undermined by grammatical errors, awkward phrasing, and a lack of clarity, with phrases like \"new tech call Artificial Intel… something (AI)\" being unprofessional. Errors such as \"India also want become big\" instead of \"India also wants to become big,\" and incorrect word choices detract from the professionalism of the writing.\\n\\nWhile the essay provides a basic overview of AI\\'s benefits and challenges in areas like agriculture, healthcare, and education, the analysis is shallow and lacks elaboration and supporting evidence. The argument is disorganized, moving between topics without clear transitions, making it difficult to follow. The use of overly simplistic language and fragmented statements further diminishes its effectiveness.\\n\\nTo improve, the essay should adopt a more structured approach, including a clear introduction and conclusion, distinct sections for benefits and challenges, and more critical analysis with specific examples. This would enhance coherence, clarity, and persuasiveness, allowing important ideas to be communicated more effectively.',\n", " 'individual_scores': [4, 3, 4],\n", " 'avg_score': 3.6666666666666665}"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["intial_state = {\n", "    'essay': essay2\n", "}\n", "\n", "workflow.invoke(intial_state)"]}, {"cell_type": "code", "execution_count": null, "id": "c3b63c3d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "myenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 5}