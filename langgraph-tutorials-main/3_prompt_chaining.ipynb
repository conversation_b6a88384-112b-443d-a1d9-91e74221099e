{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1329fdae", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph, START, END\n", "from langchain_openai import ChatOpenAI\n", "from typing import TypedDict\n", "from dotenv import load_dotenv"]}, {"cell_type": "code", "execution_count": 2, "id": "397806c4", "metadata": {}, "outputs": [], "source": ["load_dotenv()\n", "\n", "model = ChatOpenAI()"]}, {"cell_type": "code", "execution_count": 3, "id": "75ae1707", "metadata": {}, "outputs": [], "source": ["class BlogState(TypedDict):\n", "\n", "    title: str\n", "    outline: str\n", "    content: str"]}, {"cell_type": "code", "execution_count": 4, "id": "4ef1f024", "metadata": {}, "outputs": [], "source": ["def create_outline(state: BlogState) -> BlogState:\n", "\n", "    # fetch title\n", "    title = state['title']\n", "\n", "    # call llm gen outline\n", "    prompt = f'Generate a detailed outline for a blog on the topic - {title}'\n", "    outline = model.invoke(prompt).content\n", "\n", "    # update state\n", "    state['outline'] = outline\n", "\n", "    return state"]}, {"cell_type": "code", "execution_count": 5, "id": "7bab2346", "metadata": {}, "outputs": [], "source": ["def create_blog(state: BlogState) -> BlogState:\n", "\n", "    title = state['title']\n", "    outline = state['outline']\n", "\n", "    prompt = f'Write a detailed blog on the title - {title} using the follwing outline \\n {outline}'\n", "\n", "    content = model.invoke(prompt).content\n", "\n", "    state['content'] = content\n", "\n", "    return state"]}, {"cell_type": "code", "execution_count": 7, "id": "d49ba7c9", "metadata": {}, "outputs": [], "source": ["graph = StateGraph(BlogState)\n", "\n", "# nodes\n", "graph.add_node('create_outline', create_outline)\n", "graph.add_node('create_blog', create_blog)\n", "\n", "# edges\n", "graph.add_edge(START, 'create_outline')\n", "graph.add_edge('create_outline', 'create_blog')\n", "graph.add_edge('create_blog', END)\n", "\n", "workflow = graph.compile()\n"]}, {"cell_type": "code", "execution_count": 8, "id": "e724fda0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'title': 'Rise of AI in India', 'outline': 'I. Introduction\\n    A. Brief explanation of what AI (Artificial Intelligence) is\\n    B. Overview of how AI has been steadily growing in India\\n    C. Purpose of the blog - to explore the rise of AI in India and its implications\\n\\nII. Historical Context of AI in India\\n    A. Early developments in AI in India\\n    B. Government initiatives and policies to promote AI\\n    C. Rise of AI startups in India\\n\\nIII. Current State of AI in India\\n    A. Major industries leveraging AI technology in India\\n    B. Indian companies leading the way in AI innovation\\n    C. Impact of AI on the Indian economy and job market\\n\\nIV. Challenges and Opportunities\\n    A. Challenges faced by AI adoption in India\\n    B. Opportunities for growth and development in the AI sector\\n    C. Importance of AI education and skill development in India\\n\\nV. Future Outlook\\n    A. Predictions for the future of AI in India\\n    B. Potential areas of growth and innovation in AI\\n    C. Implications of AI on society and culture in India\\n\\nVI. Conclusion\\n    A. Recap of key points discussed in the blog\\n    B. Final thoughts on the rise of AI in India\\n    C. Call to action for readers to stay updated on AI developments in India.', 'content': 'I. Introduction\\n\\nA. Artificial Intelligence (AI) is a branch of computer science that aims to create intelligent machines that can perform tasks that typically require human intelligence. These tasks can include speech recognition, decision-making, visual perception, and more. \\n\\nB. The field of AI has been steadily growing in India over the past few years, with advancements being made in various industries such as healthcare, finance, education, and more. \\n\\nC. The purpose of this blog is to explore the rise of AI in India and its implications on the economy, job market, society, and culture.\\n\\nII. Historical Context of AI in India\\n\\nA. India has a rich history in AI research, with institutions like the Indian Institute of Technology (IIT) and the Indian Statistical Institute (ISI) making significant contributions to the field. \\n\\nB. The Indian government has also taken steps to promote AI development in the country, with initiatives like the National AI Portal and the National Mission on Interdisciplinary Cyber-Physical Systems (NM-ICPS). \\n\\nC. The rise of AI startups in India has also been a key factor in driving innovation in the field, with companies like Flipkart, Zomato, and Ola using AI to improve their services.\\n\\nIII. Current State of AI in India\\n\\nA. Major industries in India are leveraging AI technology to improve efficiency, reduce costs, and enhance customer experience. Industries like healthcare, fintech, and e-commerce are leading the way in AI adoption. \\n\\nB. Indian companies like Wipro, Infosys, and Tata Consultancy Services (TCS) are at the forefront of AI innovation, developing cutting-edge solutions for clients both in India and globally.\\n\\nC. The impact of AI on the Indian economy and job market has been significant, with AI creating new job opportunities in fields like data science, machine learning, and AI development.\\n\\nIV. Challenges and Opportunities\\n\\nA. Challenges faced by AI adoption in India include issues like data privacy, security concerns, and a lack of skilled professionals in the field. \\n\\nB. However, there are also many opportunities for growth and development in the AI sector, with the potential to create new revenue streams, improve efficiency, and drive innovation in various industries. \\n\\nC. The importance of AI education and skill development in India cannot be overstated, as a trained workforce is essential for the successful implementation of AI technologies.\\n\\nV. Future Outlook\\n\\nA. Predictions for the future of AI in India include continued growth in AI adoption across industries, with AI becoming an integral part of business operations in the country. \\n\\nB. Potential areas of growth and innovation in AI include the development of autonomous vehicles, AI-driven healthcare solutions, and personalized customer experiences.\\n\\nC. The implications of AI on society and culture in India are vast, with AI technology potentially changing the way we live, work, and interact with each other.\\n\\nVI. Conclusion\\n\\nA. In conclusion, the rise of AI in India presents both challenges and opportunities for the country, with the potential to drive economic growth, create new job opportunities, and improve the lives of citizens.\\n\\nB. It is important for readers to stay updated on AI developments in India and to continue learning about the field to fully understand its impact on society and the economy.\\n\\nC. As AI continues to evolve and grow in India, it is crucial for the country to embrace this technology and leverage its potential for the benefit of all.'}\n"]}], "source": ["intial_state = {'title': 'Rise of AI in India'}\n", "\n", "final_state = workflow.invoke(intial_state)\n", "\n", "print(final_state)"]}, {"cell_type": "code", "execution_count": 9, "id": "bdd8eb97", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I. Introduction\n", "    <PERSON><PERSON>rief explanation of what AI (Artificial Intelligence) is\n", "    <PERSON><PERSON> Overview of how AI has been steadily growing in India\n", "    <PERSON><PERSON> of the blog - to explore the rise of AI in India and its implications\n", "\n", "II. Historical Context of AI in India\n", "    <PERSON><PERSON> Early developments in AI in India\n", "    B. Government initiatives and policies to promote AI\n", "    C. Rise of AI startups in India\n", "\n", "III. Current State of AI in India\n", "    A. Major industries leveraging AI technology in India\n", "    B. Indian companies leading the way in AI innovation\n", "    C. Impact of AI on the Indian economy and job market\n", "\n", "IV. Challenges and Opportunities\n", "    A. Challenges faced by AI adoption in India\n", "    B. Opportunities for growth and development in the AI sector\n", "    C. Importance of AI education and skill development in India\n", "\n", "V. Future Outlook\n", "    A. Predictions for the future of AI in India\n", "    B. Potential areas of growth and innovation in AI\n", "    C. Implications of AI on society and culture in India\n", "\n", "VI. Conclusion\n", "    <PERSON><PERSON> of key points discussed in the blog\n", "    B. Final thoughts on the rise of AI in India\n", "    <PERSON><PERSON> Call to action for readers to stay updated on AI developments in India.\n"]}], "source": ["print(final_state['outline'])"]}, {"cell_type": "code", "execution_count": 10, "id": "e38569eb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I. Introduction\n", "\n", "A. Artificial Intelligence (AI) is a branch of computer science that aims to create intelligent machines that can perform tasks that typically require human intelligence. These tasks can include speech recognition, decision-making, visual perception, and more. \n", "\n", "B. The field of AI has been steadily growing in India over the past few years, with advancements being made in various industries such as healthcare, finance, education, and more. \n", "\n", "C. The purpose of this blog is to explore the rise of AI in India and its implications on the economy, job market, society, and culture.\n", "\n", "II. Historical Context of AI in India\n", "\n", "A. India has a rich history in AI research, with institutions like the Indian Institute of Technology (IIT) and the Indian Statistical Institute (ISI) making significant contributions to the field. \n", "\n", "B. The Indian government has also taken steps to promote AI development in the country, with initiatives like the National AI Portal and the National Mission on Interdisciplinary Cyber-Physical Systems (NM-ICPS). \n", "\n", "C. The rise of AI startups in India has also been a key factor in driving innovation in the field, with companies like Flipkart, Zomato, and Ola using AI to improve their services.\n", "\n", "III. Current State of AI in India\n", "\n", "A. Major industries in India are leveraging AI technology to improve efficiency, reduce costs, and enhance customer experience. Industries like healthcare, fintech, and e-commerce are leading the way in AI adoption. \n", "\n", "B. Indian companies like Wipro, Infosys, and Tata Consultancy Services (TCS) are at the forefront of AI innovation, developing cutting-edge solutions for clients both in India and globally.\n", "\n", "C. The impact of AI on the Indian economy and job market has been significant, with AI creating new job opportunities in fields like data science, machine learning, and AI development.\n", "\n", "IV. Challenges and Opportunities\n", "\n", "A. Challenges faced by AI adoption in India include issues like data privacy, security concerns, and a lack of skilled professionals in the field. \n", "\n", "B. However, there are also many opportunities for growth and development in the AI sector, with the potential to create new revenue streams, improve efficiency, and drive innovation in various industries. \n", "\n", "C. The importance of AI education and skill development in India cannot be overstated, as a trained workforce is essential for the successful implementation of AI technologies.\n", "\n", "V. Future Outlook\n", "\n", "A. Predictions for the future of AI in India include continued growth in AI adoption across industries, with AI becoming an integral part of business operations in the country. \n", "\n", "B. Potential areas of growth and innovation in AI include the development of autonomous vehicles, AI-driven healthcare solutions, and personalized customer experiences.\n", "\n", "C. The implications of AI on society and culture in India are vast, with AI technology potentially changing the way we live, work, and interact with each other.\n", "\n", "VI. Conclusion\n", "\n", "A. In conclusion, the rise of AI in India presents both challenges and opportunities for the country, with the potential to drive economic growth, create new job opportunities, and improve the lives of citizens.\n", "\n", "B. It is important for readers to stay updated on AI developments in India and to continue learning about the field to fully understand its impact on society and the economy.\n", "\n", "C. As AI continues to evolve and grow in India, it is crucial for the country to embrace this technology and leverage its potential for the benefit of all.\n"]}], "source": ["print(final_state['content'])"]}, {"cell_type": "code", "execution_count": null, "id": "ca21b153", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "myenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 5}