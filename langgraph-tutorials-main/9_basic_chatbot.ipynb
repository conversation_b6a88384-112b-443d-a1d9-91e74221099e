{"cells": [{"cell_type": "code", "execution_count": 4, "id": "e7ba8f9b", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph, START, END\n", "from typing import TypedDict, Annotated\n", "from langchain_core.messages import BaseMessage, HumanMessage\n", "from langchain_openai import ChatOpenAI"]}, {"cell_type": "code", "execution_count": 5, "id": "3a1f4f5b", "metadata": {}, "outputs": [], "source": ["from langgraph.graph.message import add_messages\n", "\n", "class ChatState(TypedDict):\n", "\n", "    messages: Annotated[list[BaseMessage], add_messages]"]}, {"cell_type": "code", "execution_count": 6, "id": "1867b09e", "metadata": {}, "outputs": [], "source": ["llm = ChatOpenAI()\n", "\n", "\n", "def chat_node(state: ChatState):\n", "\n", "    # take user query from state\n", "    messages = state['messages']\n", "\n", "    # send to llm\n", "    response = llm.invoke(messages)\n", "\n", "    # response store state\n", "    return {'messages': [response]}"]}, {"cell_type": "code", "execution_count": 7, "id": "fff8f75f", "metadata": {}, "outputs": [], "source": ["graph = StateGraph(ChatState)\n", "\n", "# add nodes\n", "graph.add_node('chat_node', chat_node)\n", "\n", "graph.add_edge(START, 'chat_node')\n", "graph.add_edge('chat_node', END)\n", "\n", "chatbot = graph.compile()"]}, {"cell_type": "code", "execution_count": 8, "id": "fbe23439", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<langgraph.graph.state.CompiledStateGraph object at 0x000001ECC9FCF610>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["chatbot"]}, {"cell_type": "code", "execution_count": 11, "id": "55ba12b1", "metadata": {}, "outputs": [{"data": {"text/plain": ["'New Delhi'"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["initial_state = {\n", "    'messages': [HumanMessage(content='What is the capital of india')]\n", "}\n", "\n", "chatbot.invoke(initial_state)['messages'][-1].content"]}, {"cell_type": "code", "execution_count": null, "id": "0a569f4a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "myenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 5}