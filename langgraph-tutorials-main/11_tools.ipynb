{"cells": [{"cell_type": "code", "execution_count": 56, "id": "56c3286a", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph, START, END\n", "from typing import TypedDict, Annotated\n", "from langchain_core.messages import BaseMessage, HumanMessage\n", "from langchain_openai import ChatOpenAI\n", "from langgraph.graph.message import add_messages\n", "from dotenv import load_dotenv\n", "\n", "from langgraph.prebuilt import ToolNode, tools_condition\n", "from langchain_community.tools import DuckDuckGoSearchRun\n", "from langchain_core.tools import tool\n", "\n", "import requests\n", "import random"]}, {"cell_type": "code", "execution_count": 57, "id": "587d6db4", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["load_dotenv()"]}, {"cell_type": "code", "execution_count": 58, "id": "53d2cdab", "metadata": {}, "outputs": [], "source": ["llm = ChatOpenAI()"]}, {"cell_type": "code", "execution_count": 59, "id": "7571c160", "metadata": {}, "outputs": [], "source": ["# Tools\n", "search_tool = DuckDuckGoSearchRun(region=\"us-en\")\n", "\n", "@tool\n", "def calculator(first_num: float, second_num: float, operation: str) -> dict:\n", "    \"\"\"\n", "    Perform a basic arithmetic operation on two numbers.\n", "    Supported operations: add, sub, mul, div\n", "    \"\"\"\n", "    try:\n", "        if operation == \"add\":\n", "            result = first_num + second_num\n", "        elif operation == \"sub\":\n", "            result = first_num - second_num\n", "        elif operation == \"mul\":\n", "            result = first_num * second_num\n", "        elif operation == \"div\":\n", "            if second_num == 0:\n", "                return {\"error\": \"Division by zero is not allowed\"}\n", "            result = first_num / second_num\n", "        else:\n", "            return {\"error\": f\"Unsupported operation '{operation}'\"}\n", "        \n", "        return {\"first_num\": first_num, \"second_num\": second_num, \"operation\": operation, \"result\": result}\n", "    except Exception as e:\n", "        return {\"error\": str(e)}\n", "\n", "\n", "@tool\n", "def get_stock_price(symbol: str) -> dict:\n", "    \"\"\"\n", "    Fetch latest stock price for a given symbol (e.g. 'AAPL', 'TSLA') \n", "    using Alpha Vantage with API key in the URL.\n", "    \"\"\"\n", "    url = f\"https://www.alphavantage.co/query?function=GLOBAL_QUOTE&symbol={symbol}&apikey=C9PE94QUEW9VWGFM\"\n", "    r = requests.get(url)\n", "    return r.json()\n"]}, {"cell_type": "code", "execution_count": 60, "id": "fb8927e6", "metadata": {}, "outputs": [], "source": ["# Make tool list\n", "tools = [get_stock_price, search_tool, calculator]\n", "\n", "# Make the LLM tool-aware\n", "llm_with_tools = llm.bind_tools(tools)"]}, {"cell_type": "code", "execution_count": 62, "id": "19de64c4", "metadata": {}, "outputs": [], "source": ["# state\n", "class ChatState(TypedDict):\n", "    messages: Annotated[list[BaseMessage], add_messages]"]}, {"cell_type": "code", "execution_count": 63, "id": "2e7a17e2", "metadata": {}, "outputs": [], "source": ["# graph nodes\n", "def chat_node(state: ChatState):\n", "    \"\"\"LLM node that may answer or request a tool call.\"\"\"\n", "    messages = state['messages']\n", "    response = llm_with_tools.invoke(messages)\n", "    return {\"messages\": [response]}\n", "\n", "tool_node = ToolNode(tools)  # Executes tool calls"]}, {"cell_type": "code", "execution_count": 71, "id": "671de9a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["<langgraph.graph.state.StateGraph at 0x11c193110>"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["# graph structure\n", "graph = StateGraph(ChatState)\n", "graph.add_node(\"chat_node\", chat_node)\n", "graph.add_node(\"tools\", tool_node)"]}, {"cell_type": "code", "execution_count": 72, "id": "90aca121", "metadata": {}, "outputs": [{"data": {"text/plain": ["<langgraph.graph.state.StateGraph at 0x11c193110>"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["graph.add_edge(START, \"chat_node\")\n", "\n", "# If the LLM asked for a tool, go to ToolNode; else finish\n", "graph.add_conditional_edges(\"chat_node\", tools_condition)\n", "\n", "graph.add_edge(\"tools\", \"chat_node\")    "]}, {"cell_type": "code", "execution_count": 73, "id": "0872d919", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<langgraph.graph.state.CompiledStateGraph object at 0x11c24e1d0>"]}, "execution_count": 73, "metadata": {}, "output_type": "execute_result"}], "source": ["chatbot = graph.compile()\n", "\n", "chatbot"]}, {"cell_type": "code", "execution_count": 74, "id": "118a4ec7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello! How can I assist you today?\n"]}], "source": ["# Regular chat\n", "out = chatbot.invoke({\"messages\": [HumanMessage(content=\"Hello!\")]})\n", "\n", "print(out[\"messages\"][-1].content)\n"]}, {"cell_type": "code", "execution_count": 75, "id": "8f904078", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The result of 2 multiplied by 3 is 6.\n"]}], "source": ["# Chat requiring tool\n", "out = chatbot.invoke({\"messages\": [HumanMessage(content=\"What is 2*3?\")]})\n", "print(out[\"messages\"][-1].content)"]}, {"cell_type": "code", "execution_count": 76, "id": "004fc45e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The stock price of Apple (AAPL) is $227.76.\n"]}], "source": ["# Chat requiring tool\n", "out = chatbot.invoke({\"messages\": [HumanMessage(content=\"What is the stock price of apple\")]})\n", "print(out[\"messages\"][-1].content)"]}, {"cell_type": "code", "execution_count": 78, "id": "5b69cb8d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The current stock price of Apple (AAPL) is $227.76 per share. \n", "\n", "To purchase 50 shares of Apple, it would cost $11,388.00.\n"]}], "source": ["# Chat requiring tool\n", "out = chatbot.invoke({\"messages\": [HumanMessage(content=\"First find out the stock price of Apple using get stock price tool then use the calculator tool to find out how much will it take to purchase 50 shares?\")]})\n", "print(out[\"messages\"][-1].content)"]}, {"cell_type": "code", "execution_count": null, "id": "79e56952", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "myenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}