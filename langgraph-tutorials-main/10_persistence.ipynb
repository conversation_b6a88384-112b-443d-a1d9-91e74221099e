{"cells": [{"cell_type": "code", "execution_count": 98, "id": "5fe01464", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph, START, END\n", "from typing import TypedDict\n", "from langchain_openai import ChatOpenAI\n", "from dotenv import load_dotenv\n", "from langgraph.checkpoint.memory import InMemorySaver"]}, {"cell_type": "code", "execution_count": 99, "id": "d65bfc11", "metadata": {}, "outputs": [], "source": ["load_dotenv()\n", "\n", "llm = ChatOpenAI()"]}, {"cell_type": "code", "execution_count": 100, "id": "5922b899", "metadata": {}, "outputs": [], "source": ["class JokeState(TypedDict):\n", "\n", "    topic: str\n", "    joke: str\n", "    explanation: str"]}, {"cell_type": "code", "execution_count": 101, "id": "0561076c", "metadata": {}, "outputs": [], "source": ["def generate_joke(state: JokeState):\n", "\n", "    prompt = f'generate a joke on the topic {state[\"topic\"]}'\n", "    response = llm.invoke(prompt).content\n", "\n", "    return {'joke': response}"]}, {"cell_type": "code", "execution_count": 102, "id": "d95ff880", "metadata": {}, "outputs": [], "source": ["def generate_explanation(state: JokeState):\n", "\n", "    prompt = f'write an explanation for the joke - {state[\"joke\"]}'\n", "    response = llm.invoke(prompt).content\n", "\n", "    return {'explanation': response}"]}, {"cell_type": "code", "execution_count": 103, "id": "a1093470", "metadata": {}, "outputs": [], "source": ["graph = StateGraph(JokeState)\n", "\n", "graph.add_node('generate_joke', generate_joke)\n", "graph.add_node('generate_explanation', generate_explanation)\n", "\n", "graph.add_edge(START, 'generate_joke')\n", "graph.add_edge('generate_joke', 'generate_explanation')\n", "graph.add_edge('generate_explanation', END)\n", "\n", "checkpointer = InMemorySaver()\n", "\n", "workflow = graph.compile(checkpointer=checkpointer)"]}, {"cell_type": "code", "execution_count": 104, "id": "5e8f11c8", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'topic': 'pizza',\n", " 'joke': 'Why did the pizza go to the doctor? Because it was feeling a little saucy!',\n", " 'explanation': 'This joke plays on the double meaning of the word \"saucy.\" In one sense, \"saucy\" can mean bold, impertinent, or sassy. But in the context of a pizza, \"saucy\" refers to the tomato sauce typically used as a base on pizzas. So when the pizza went to the doctor because it was feeling \"saucy,\" it implies that the pizza was not feeling well due to too much sauce, rather than being bold or sassy. The humor comes from the unexpected twist on the word\\'s meaning.'}"]}, "execution_count": 104, "metadata": {}, "output_type": "execute_result"}], "source": ["config1 = {\"configurable\": {\"thread_id\": \"1\"}}\n", "workflow.invoke({'topic':'pizza'}, config=config1)"]}, {"cell_type": "code", "execution_count": 105, "id": "a6857b9c", "metadata": {}, "outputs": [{"data": {"text/plain": ["StateSnapshot(values={'topic': 'pizza', 'joke': 'Why did the pizza go to the doctor? Because it was feeling a little saucy!', 'explanation': 'This joke plays on the double meaning of the word \"saucy.\" In one sense, \"saucy\" can mean bold, impertinent, or sassy. But in the context of a pizza, \"saucy\" refers to the tomato sauce typically used as a base on pizzas. So when the pizza went to the doctor because it was feeling \"saucy,\" it implies that the pizza was not feeling well due to too much sauce, rather than being bold or sassy. The humor comes from the unexpected twist on the word\\'s meaning.'}, next=(), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-93a2-6a08-8002-395e36be0f5e'}}, metadata={'source': 'loop', 'step': 2, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:56:42.071296+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7a2f-60ea-8001-4ac26c539f8d'}}, tasks=(), interrupts=())"]}, "execution_count": 105, "metadata": {}, "output_type": "execute_result"}], "source": ["workflow.get_state(config1)"]}, {"cell_type": "code", "execution_count": 106, "id": "eb9eecda", "metadata": {}, "outputs": [{"data": {"text/plain": ["[StateSnapshot(values={'topic': 'pizza', 'joke': 'Why did the pizza go to the doctor? Because it was feeling a little saucy!', 'explanation': 'This joke plays on the double meaning of the word \"saucy.\" In one sense, \"saucy\" can mean bold, impertinent, or sassy. But in the context of a pizza, \"saucy\" refers to the tomato sauce typically used as a base on pizzas. So when the pizza went to the doctor because it was feeling \"saucy,\" it implies that the pizza was not feeling well due to too much sauce, rather than being bold or sassy. The humor comes from the unexpected twist on the word\\'s meaning.'}, next=(), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-93a2-6a08-8002-395e36be0f5e'}}, metadata={'source': 'loop', 'step': 2, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:56:42.071296+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7a2f-60ea-8001-4ac26c539f8d'}}, tasks=(), interrupts=()),\n", " StateSnapshot(values={'topic': 'pizza', 'joke': 'Why did the pizza go to the doctor? Because it was feeling a little saucy!'}, next=('generate_explanation',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7a2f-60ea-8001-4ac26c539f8d'}}, metadata={'source': 'loop', 'step': 1, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:56:39.402519+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7232-6cb1-8000-f71609e6cec5'}}, tasks=(PregelTask(id='1f8e036d-b599-6074-0dac-c200937328d4', name='generate_explanation', path=('__pregel_pull', 'generate_explanation'), error=None, interrupts=(), state=None, result={'explanation': 'This joke plays on the double meaning of the word \"saucy.\" In one sense, \"saucy\" can mean bold, impertinent, or sassy. But in the context of a pizza, \"saucy\" refers to the tomato sauce typically used as a base on pizzas. So when the pizza went to the doctor because it was feeling \"saucy,\" it implies that the pizza was not feeling well due to too much sauce, rather than being bold or sassy. The humor comes from the unexpected twist on the word\\'s meaning.'}),), interrupts=()),\n", " StateSnapshot(values={'topic': 'pizza'}, next=('generate_joke',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7232-6cb1-8000-f71609e6cec5'}}, metadata={'source': 'loop', 'step': 0, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:56:38.565188+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7230-65a8-bfff-0a96c2fc4e11'}}, tasks=(PregelTask(id='dcd96e38-1f32-5ed6-9f44-fa2b22c193f0', name='generate_joke', path=('__pregel_pull', 'generate_joke'), error=None, interrupts=(), state=None, result={'joke': 'Why did the pizza go to the doctor? Because it was feeling a little saucy!'}),), interrupts=()),\n", " StateSnapshot(values={}, next=('__start__',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7230-65a8-bfff-0a96c2fc4e11'}}, metadata={'source': 'input', 'step': -1, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:56:38.564189+00:00', parent_config=None, tasks=(PregelTask(id='2d4d902e-419f-6dff-1b83-385121fc185e', name='__start__', path=('__pregel_pull', '__start__'), error=None, interrupts=(), state=None, result={'topic': 'pizza'}),), interrupts=())]"]}, "execution_count": 106, "metadata": {}, "output_type": "execute_result"}], "source": ["list(workflow.get_state_history(config1))"]}, {"cell_type": "code", "execution_count": 107, "id": "628a9f28", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'topic': 'pasta',\n", " 'joke': 'Why did the spaghetti sit down at the dinner table? \\nBecause it was pasta-tively exhausted from all that boiling!',\n", " 'explanation': 'This joke plays on the idea that pasta needs to be boiled in order to be cooked and ready to eat. The punchline, \"pasta-tively exhausted,\" is a play on words between \"positively exhausted\" and \"pasta,\" highlighting the fact that the spaghetti was tired from being boiled. The humor lies in the personification of the spaghetti as if it has feelings and actions, such as sitting down at the dinner table.'}"]}, "execution_count": 107, "metadata": {}, "output_type": "execute_result"}], "source": ["config2 = {\"configurable\": {\"thread_id\": \"2\"}}\n", "workflow.invoke({'topic':'pasta'}, config=config2)"]}, {"cell_type": "code", "execution_count": 108, "id": "eef5beb5", "metadata": {}, "outputs": [{"data": {"text/plain": ["StateSnapshot(values={'topic': 'pizza', 'joke': 'Why did the pizza go to the doctor? Because it was feeling a little saucy!', 'explanation': 'This joke plays on the double meaning of the word \"saucy.\" In one sense, \"saucy\" can mean bold, impertinent, or sassy. But in the context of a pizza, \"saucy\" refers to the tomato sauce typically used as a base on pizzas. So when the pizza went to the doctor because it was feeling \"saucy,\" it implies that the pizza was not feeling well due to too much sauce, rather than being bold or sassy. The humor comes from the unexpected twist on the word\\'s meaning.'}, next=(), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-93a2-6a08-8002-395e36be0f5e'}}, metadata={'source': 'loop', 'step': 2, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:56:42.071296+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7a2f-60ea-8001-4ac26c539f8d'}}, tasks=(), interrupts=())"]}, "execution_count": 108, "metadata": {}, "output_type": "execute_result"}], "source": ["workflow.get_state(config1)"]}, {"cell_type": "code", "execution_count": 109, "id": "f7d0ea21", "metadata": {}, "outputs": [{"data": {"text/plain": ["[StateSnapshot(values={'topic': 'pizza', 'joke': 'Why did the pizza go to the doctor? Because it was feeling a little saucy!', 'explanation': 'This joke plays on the double meaning of the word \"saucy.\" In one sense, \"saucy\" can mean bold, impertinent, or sassy. But in the context of a pizza, \"saucy\" refers to the tomato sauce typically used as a base on pizzas. So when the pizza went to the doctor because it was feeling \"saucy,\" it implies that the pizza was not feeling well due to too much sauce, rather than being bold or sassy. The humor comes from the unexpected twist on the word\\'s meaning.'}, next=(), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-93a2-6a08-8002-395e36be0f5e'}}, metadata={'source': 'loop', 'step': 2, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:56:42.071296+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7a2f-60ea-8001-4ac26c539f8d'}}, tasks=(), interrupts=()),\n", " StateSnapshot(values={'topic': 'pizza', 'joke': 'Why did the pizza go to the doctor? Because it was feeling a little saucy!'}, next=('generate_explanation',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7a2f-60ea-8001-4ac26c539f8d'}}, metadata={'source': 'loop', 'step': 1, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:56:39.402519+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7232-6cb1-8000-f71609e6cec5'}}, tasks=(PregelTask(id='1f8e036d-b599-6074-0dac-c200937328d4', name='generate_explanation', path=('__pregel_pull', 'generate_explanation'), error=None, interrupts=(), state=None, result={'explanation': 'This joke plays on the double meaning of the word \"saucy.\" In one sense, \"saucy\" can mean bold, impertinent, or sassy. But in the context of a pizza, \"saucy\" refers to the tomato sauce typically used as a base on pizzas. So when the pizza went to the doctor because it was feeling \"saucy,\" it implies that the pizza was not feeling well due to too much sauce, rather than being bold or sassy. The humor comes from the unexpected twist on the word\\'s meaning.'}),), interrupts=()),\n", " StateSnapshot(values={'topic': 'pizza'}, next=('generate_joke',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7232-6cb1-8000-f71609e6cec5'}}, metadata={'source': 'loop', 'step': 0, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:56:38.565188+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7230-65a8-bfff-0a96c2fc4e11'}}, tasks=(PregelTask(id='dcd96e38-1f32-5ed6-9f44-fa2b22c193f0', name='generate_joke', path=('__pregel_pull', 'generate_joke'), error=None, interrupts=(), state=None, result={'joke': 'Why did the pizza go to the doctor? Because it was feeling a little saucy!'}),), interrupts=()),\n", " StateSnapshot(values={}, next=('__start__',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7230-65a8-bfff-0a96c2fc4e11'}}, metadata={'source': 'input', 'step': -1, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:56:38.564189+00:00', parent_config=None, tasks=(PregelTask(id='2d4d902e-419f-6dff-1b83-385121fc185e', name='__start__', path=('__pregel_pull', '__start__'), error=None, interrupts=(), state=None, result={'topic': 'pizza'}),), interrupts=())]"]}, "execution_count": 109, "metadata": {}, "output_type": "execute_result"}], "source": ["list(workflow.get_state_history(config1))"]}, {"cell_type": "markdown", "id": "5fccccee", "metadata": {}, "source": ["### Time Travel"]}, {"cell_type": "code", "execution_count": 110, "id": "b5bfa235", "metadata": {}, "outputs": [{"data": {"text/plain": ["StateSnapshot(values={'topic': 'pizza'}, next=('generate_joke',), config={'configurable': {'thread_id': '1', 'checkpoint_id': '1f06cc6e-7232-6cb1-8000-f71609e6cec5'}}, metadata={'source': 'loop', 'step': 0, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:56:38.565188+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7230-65a8-bfff-0a96c2fc4e11'}}, tasks=(PregelTask(id='dcd96e38-1f32-5ed6-9f44-fa2b22c193f0', name='generate_joke', path=('__pregel_pull', 'generate_joke'), error=None, interrupts=(), state=None, result={'joke': 'Why did the pizza go to the doctor? Because it was feeling a little saucy!'}),), interrupts=())"]}, "execution_count": 110, "metadata": {}, "output_type": "execute_result"}], "source": ["workflow.get_state({\"configurable\": {\"thread_id\": \"1\", \"checkpoint_id\": \"1f06cc6e-7232-6cb1-8000-f71609e6cec5\"}})"]}, {"cell_type": "code", "execution_count": 111, "id": "1a7d2e73", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'topic': 'pizza',\n", " 'joke': 'Why did the mushroom go to the pizza party? Because he was a fungi and everyone wanted a pizza him!',\n", " 'explanation': 'This joke plays on the word \"fun guy\" (fungi) which sounds like \"fungi,\" a type of mushroom. The play on words is that the mushroom went to the pizza party because he was a \"fun guy\" and people wanted to \"pizza\" (see) him. The joke is a pun that combines the idea of mushrooms being fungi with the concept of being a fun person at a party.'}"]}, "execution_count": 111, "metadata": {}, "output_type": "execute_result"}], "source": ["workflow.invoke(None, {\"configurable\": {\"thread_id\": \"1\", \"checkpoint_id\": \"1f06cc6e-7232-6cb1-8000-f71609e6cec5\"}})"]}, {"cell_type": "code", "execution_count": 112, "id": "cd55cf35", "metadata": {}, "outputs": [{"data": {"text/plain": ["[StateSnapshot(values={'topic': 'pizza', 'joke': 'Why did the mushroom go to the pizza party? Because he was a fungi and everyone wanted a pizza him!', 'explanation': 'This joke plays on the word \"fun guy\" (fungi) which sounds like \"fungi,\" a type of mushroom. The play on words is that the mushroom went to the pizza party because he was a \"fun guy\" and people wanted to \"pizza\" (see) him. The joke is a pun that combines the idea of mushrooms being fungi with the concept of being a fun person at a party.'}, next=(), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc70-100a-6bff-8002-7d6c3d37b1f4'}}, metadata={'source': 'loop', 'step': 2, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:57:21.959833+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc70-064c-630b-8001-707d60a085ad'}}, tasks=(), interrupts=()),\n", " StateSnapshot(values={'topic': 'pizza', 'joke': 'Why did the mushroom go to the pizza party? Because he was a fungi and everyone wanted a pizza him!'}, next=('generate_explanation',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc70-064c-630b-8001-707d60a085ad'}}, metadata={'source': 'loop', 'step': 1, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:57:20.938061+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7232-6cb1-8000-f71609e6cec5'}}, tasks=(PregelTask(id='d5be7a49-5b24-1968-3d22-a35343a9ae76', name='generate_explanation', path=('__pregel_pull', 'generate_explanation'), error=None, interrupts=(), state=None, result={'explanation': 'This joke plays on the word \"fun guy\" (fungi) which sounds like \"fungi,\" a type of mushroom. The play on words is that the mushroom went to the pizza party because he was a \"fun guy\" and people wanted to \"pizza\" (see) him. The joke is a pun that combines the idea of mushrooms being fungi with the concept of being a fun person at a party.'}),), interrupts=()),\n", " StateSnapshot(values={'topic': 'pizza', 'joke': 'Why did the pizza go to the doctor? Because it was feeling a little saucy!', 'explanation': 'This joke plays on the double meaning of the word \"saucy.\" In one sense, \"saucy\" can mean bold, impertinent, or sassy. But in the context of a pizza, \"saucy\" refers to the tomato sauce typically used as a base on pizzas. So when the pizza went to the doctor because it was feeling \"saucy,\" it implies that the pizza was not feeling well due to too much sauce, rather than being bold or sassy. The humor comes from the unexpected twist on the word\\'s meaning.'}, next=(), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-93a2-6a08-8002-395e36be0f5e'}}, metadata={'source': 'loop', 'step': 2, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:56:42.071296+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7a2f-60ea-8001-4ac26c539f8d'}}, tasks=(), interrupts=()),\n", " StateSnapshot(values={'topic': 'pizza', 'joke': 'Why did the pizza go to the doctor? Because it was feeling a little saucy!'}, next=('generate_explanation',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7a2f-60ea-8001-4ac26c539f8d'}}, metadata={'source': 'loop', 'step': 1, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:56:39.402519+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7232-6cb1-8000-f71609e6cec5'}}, tasks=(PregelTask(id='1f8e036d-b599-6074-0dac-c200937328d4', name='generate_explanation', path=('__pregel_pull', 'generate_explanation'), error=None, interrupts=(), state=None, result={'explanation': 'This joke plays on the double meaning of the word \"saucy.\" In one sense, \"saucy\" can mean bold, impertinent, or sassy. But in the context of a pizza, \"saucy\" refers to the tomato sauce typically used as a base on pizzas. So when the pizza went to the doctor because it was feeling \"saucy,\" it implies that the pizza was not feeling well due to too much sauce, rather than being bold or sassy. The humor comes from the unexpected twist on the word\\'s meaning.'}),), interrupts=()),\n", " StateSnapshot(values={'topic': 'pizza'}, next=('generate_joke',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7232-6cb1-8000-f71609e6cec5'}}, metadata={'source': 'loop', 'step': 0, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:56:38.565188+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7230-65a8-bfff-0a96c2fc4e11'}}, tasks=(PregelTask(id='dcd96e38-1f32-5ed6-9f44-fa2b22c193f0', name='generate_joke', path=('__pregel_pull', 'generate_joke'), error=None, interrupts=(), state=None, result={'joke': 'Why did the pizza go to the doctor? Because it was feeling a little saucy!'}),), interrupts=()),\n", " StateSnapshot(values={}, next=('__start__',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7230-65a8-bfff-0a96c2fc4e11'}}, metadata={'source': 'input', 'step': -1, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:56:38.564189+00:00', parent_config=None, tasks=(PregelTask(id='2d4d902e-419f-6dff-1b83-385121fc185e', name='__start__', path=('__pregel_pull', '__start__'), error=None, interrupts=(), state=None, result={'topic': 'pizza'}),), interrupts=())]"]}, "execution_count": 112, "metadata": {}, "output_type": "execute_result"}], "source": ["list(workflow.get_state_history(config1))"]}, {"cell_type": "markdown", "id": "ab1b895e", "metadata": {}, "source": ["#### Updating State"]}, {"cell_type": "code", "execution_count": 113, "id": "19378d86", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'configurable': {'thread_id': '1',\n", "  'checkpoint_ns': '',\n", "  'checkpoint_id': '1f06cc72-ca16-6359-8001-7eea05e07dd2'}}"]}, "execution_count": 113, "metadata": {}, "output_type": "execute_result"}], "source": ["workflow.update_state({\"configurable\": {\"thread_id\": \"1\", \"checkpoint_id\": \"1f06cc6e-7232-6cb1-8000-f71609e6cec5\", \"checkpoint_ns\": \"\"}}, {'topic':'samosa'})"]}, {"cell_type": "code", "execution_count": 114, "id": "7c486bff", "metadata": {}, "outputs": [{"data": {"text/plain": ["[StateSnapshot(values={'topic': 'samosa'}, next=('generate_joke',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc72-ca16-6359-8001-7eea05e07dd2'}}, metadata={'source': 'update', 'step': 1, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:58:35.155132+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7232-6cb1-8000-f71609e6cec5'}}, tasks=(PregelTask(id='0f085bb0-c1e8-d9fd-fb15-c427126b7cd6', name='generate_joke', path=('__pregel_pull', 'generate_joke'), error=None, interrupts=(), state=None, result=None),), interrupts=()),\n", " StateSnapshot(values={'topic': 'pizza', 'joke': 'Why did the mushroom go to the pizza party? Because he was a fungi and everyone wanted a pizza him!', 'explanation': 'This joke plays on the word \"fun guy\" (fungi) which sounds like \"fungi,\" a type of mushroom. The play on words is that the mushroom went to the pizza party because he was a \"fun guy\" and people wanted to \"pizza\" (see) him. The joke is a pun that combines the idea of mushrooms being fungi with the concept of being a fun person at a party.'}, next=(), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc70-100a-6bff-8002-7d6c3d37b1f4'}}, metadata={'source': 'loop', 'step': 2, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:57:21.959833+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc70-064c-630b-8001-707d60a085ad'}}, tasks=(), interrupts=()),\n", " StateSnapshot(values={'topic': 'pizza', 'joke': 'Why did the mushroom go to the pizza party? Because he was a fungi and everyone wanted a pizza him!'}, next=('generate_explanation',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc70-064c-630b-8001-707d60a085ad'}}, metadata={'source': 'loop', 'step': 1, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:57:20.938061+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7232-6cb1-8000-f71609e6cec5'}}, tasks=(PregelTask(id='d5be7a49-5b24-1968-3d22-a35343a9ae76', name='generate_explanation', path=('__pregel_pull', 'generate_explanation'), error=None, interrupts=(), state=None, result={'explanation': 'This joke plays on the word \"fun guy\" (fungi) which sounds like \"fungi,\" a type of mushroom. The play on words is that the mushroom went to the pizza party because he was a \"fun guy\" and people wanted to \"pizza\" (see) him. The joke is a pun that combines the idea of mushrooms being fungi with the concept of being a fun person at a party.'}),), interrupts=()),\n", " StateSnapshot(values={'topic': 'pizza', 'joke': 'Why did the pizza go to the doctor? Because it was feeling a little saucy!', 'explanation': 'This joke plays on the double meaning of the word \"saucy.\" In one sense, \"saucy\" can mean bold, impertinent, or sassy. But in the context of a pizza, \"saucy\" refers to the tomato sauce typically used as a base on pizzas. So when the pizza went to the doctor because it was feeling \"saucy,\" it implies that the pizza was not feeling well due to too much sauce, rather than being bold or sassy. The humor comes from the unexpected twist on the word\\'s meaning.'}, next=(), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-93a2-6a08-8002-395e36be0f5e'}}, metadata={'source': 'loop', 'step': 2, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:56:42.071296+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7a2f-60ea-8001-4ac26c539f8d'}}, tasks=(), interrupts=()),\n", " StateSnapshot(values={'topic': 'pizza', 'joke': 'Why did the pizza go to the doctor? Because it was feeling a little saucy!'}, next=('generate_explanation',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7a2f-60ea-8001-4ac26c539f8d'}}, metadata={'source': 'loop', 'step': 1, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:56:39.402519+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7232-6cb1-8000-f71609e6cec5'}}, tasks=(PregelTask(id='1f8e036d-b599-6074-0dac-c200937328d4', name='generate_explanation', path=('__pregel_pull', 'generate_explanation'), error=None, interrupts=(), state=None, result={'explanation': 'This joke plays on the double meaning of the word \"saucy.\" In one sense, \"saucy\" can mean bold, impertinent, or sassy. But in the context of a pizza, \"saucy\" refers to the tomato sauce typically used as a base on pizzas. So when the pizza went to the doctor because it was feeling \"saucy,\" it implies that the pizza was not feeling well due to too much sauce, rather than being bold or sassy. The humor comes from the unexpected twist on the word\\'s meaning.'}),), interrupts=()),\n", " StateSnapshot(values={'topic': 'pizza'}, next=('generate_joke',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7232-6cb1-8000-f71609e6cec5'}}, metadata={'source': 'loop', 'step': 0, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:56:38.565188+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7230-65a8-bfff-0a96c2fc4e11'}}, tasks=(PregelTask(id='dcd96e38-1f32-5ed6-9f44-fa2b22c193f0', name='generate_joke', path=('__pregel_pull', 'generate_joke'), error=None, interrupts=(), state=None, result={'joke': 'Why did the pizza go to the doctor? Because it was feeling a little saucy!'}),), interrupts=()),\n", " StateSnapshot(values={}, next=('__start__',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7230-65a8-bfff-0a96c2fc4e11'}}, metadata={'source': 'input', 'step': -1, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:56:38.564189+00:00', parent_config=None, tasks=(PregelTask(id='2d4d902e-419f-6dff-1b83-385121fc185e', name='__start__', path=('__pregel_pull', '__start__'), error=None, interrupts=(), state=None, result={'topic': 'pizza'}),), interrupts=())]"]}, "execution_count": 114, "metadata": {}, "output_type": "execute_result"}], "source": ["list(workflow.get_state_history(config1))"]}, {"cell_type": "code", "execution_count": 115, "id": "58f63a80", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'topic': 'samosa',\n", " 'joke': 'Why did the samosa bring a ladder to the party? \\nBecause it wanted to be the best snack in the room and rise to the occasion!',\n", " 'explanation': 'This joke plays on the double meaning of the word \"rise.\" In one sense, \"rise\" means to physically move upwards, which is why the samosa brought a ladder to the party. However, in another sense, \"rise\" can also mean to perform well or excel, as in rising to the occasion. So, the samosa brought a ladder to symbolize its desire to physically rise above the other snacks at the party and also to metaphorically rise to the occasion by being the best snack in the room.'}"]}, "execution_count": 115, "metadata": {}, "output_type": "execute_result"}], "source": ["workflow.invoke(None, {\"configurable\": {\"thread_id\": \"1\", \"checkpoint_id\": \"1f06cc72-ca16-6359-8001-7eea05e07dd2\"}})"]}, {"cell_type": "code", "execution_count": 116, "id": "db645da9", "metadata": {}, "outputs": [{"data": {"text/plain": ["[StateSnapshot(values={'topic': 'samosa', 'joke': 'Why did the samosa bring a ladder to the party? \\nBecause it wanted to be the best snack in the room and rise to the occasion!', 'explanation': 'This joke plays on the double meaning of the word \"rise.\" In one sense, \"rise\" means to physically move upwards, which is why the samosa brought a ladder to the party. However, in another sense, \"rise\" can also mean to perform well or excel, as in rising to the occasion. So, the samosa brought a ladder to symbolize its desire to physically rise above the other snacks at the party and also to metaphorically rise to the occasion by being the best snack in the room.'}, next=(), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc75-4407-6195-8003-b08dcfd27511'}}, metadata={'source': 'loop', 'step': 3, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:59:41.628661+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc75-334a-69a5-8002-e7b30238fdad'}}, tasks=(), interrupts=()),\n", " StateSnapshot(values={'topic': 'samosa', 'joke': 'Why did the samosa bring a ladder to the party? \\nBecause it wanted to be the best snack in the room and rise to the occasion!'}, next=('generate_explanation',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc75-334a-69a5-8002-e7b30238fdad'}}, metadata={'source': 'loop', 'step': 2, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:59:39.873731+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc72-ca16-6359-8001-7eea05e07dd2'}}, tasks=(PregelTask(id='f1746623-5449-6d92-099f-5d303c27d887', name='generate_explanation', path=('__pregel_pull', 'generate_explanation'), error=None, interrupts=(), state=None, result={'explanation': 'This joke plays on the double meaning of the word \"rise.\" In one sense, \"rise\" means to physically move upwards, which is why the samosa brought a ladder to the party. However, in another sense, \"rise\" can also mean to perform well or excel, as in rising to the occasion. So, the samosa brought a ladder to symbolize its desire to physically rise above the other snacks at the party and also to metaphorically rise to the occasion by being the best snack in the room.'}),), interrupts=()),\n", " StateSnapshot(values={'topic': 'samosa'}, next=('generate_joke',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc72-ca16-6359-8001-7eea05e07dd2'}}, metadata={'source': 'update', 'step': 1, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:58:35.155132+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7232-6cb1-8000-f71609e6cec5'}}, tasks=(PregelTask(id='0f085bb0-c1e8-d9fd-fb15-c427126b7cd6', name='generate_joke', path=('__pregel_pull', 'generate_joke'), error=None, interrupts=(), state=None, result={'joke': 'Why did the samosa bring a ladder to the party? \\nBecause it wanted to be the best snack in the room and rise to the occasion!'}),), interrupts=()),\n", " StateSnapshot(values={'topic': 'pizza', 'joke': 'Why did the mushroom go to the pizza party? Because he was a fungi and everyone wanted a pizza him!', 'explanation': 'This joke plays on the word \"fun guy\" (fungi) which sounds like \"fungi,\" a type of mushroom. The play on words is that the mushroom went to the pizza party because he was a \"fun guy\" and people wanted to \"pizza\" (see) him. The joke is a pun that combines the idea of mushrooms being fungi with the concept of being a fun person at a party.'}, next=(), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc70-100a-6bff-8002-7d6c3d37b1f4'}}, metadata={'source': 'loop', 'step': 2, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:57:21.959833+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc70-064c-630b-8001-707d60a085ad'}}, tasks=(), interrupts=()),\n", " StateSnapshot(values={'topic': 'pizza', 'joke': 'Why did the mushroom go to the pizza party? Because he was a fungi and everyone wanted a pizza him!'}, next=('generate_explanation',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc70-064c-630b-8001-707d60a085ad'}}, metadata={'source': 'loop', 'step': 1, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:57:20.938061+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7232-6cb1-8000-f71609e6cec5'}}, tasks=(PregelTask(id='d5be7a49-5b24-1968-3d22-a35343a9ae76', name='generate_explanation', path=('__pregel_pull', 'generate_explanation'), error=None, interrupts=(), state=None, result={'explanation': 'This joke plays on the word \"fun guy\" (fungi) which sounds like \"fungi,\" a type of mushroom. The play on words is that the mushroom went to the pizza party because he was a \"fun guy\" and people wanted to \"pizza\" (see) him. The joke is a pun that combines the idea of mushrooms being fungi with the concept of being a fun person at a party.'}),), interrupts=()),\n", " StateSnapshot(values={'topic': 'pizza', 'joke': 'Why did the pizza go to the doctor? Because it was feeling a little saucy!', 'explanation': 'This joke plays on the double meaning of the word \"saucy.\" In one sense, \"saucy\" can mean bold, impertinent, or sassy. But in the context of a pizza, \"saucy\" refers to the tomato sauce typically used as a base on pizzas. So when the pizza went to the doctor because it was feeling \"saucy,\" it implies that the pizza was not feeling well due to too much sauce, rather than being bold or sassy. The humor comes from the unexpected twist on the word\\'s meaning.'}, next=(), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-93a2-6a08-8002-395e36be0f5e'}}, metadata={'source': 'loop', 'step': 2, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:56:42.071296+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7a2f-60ea-8001-4ac26c539f8d'}}, tasks=(), interrupts=()),\n", " StateSnapshot(values={'topic': 'pizza', 'joke': 'Why did the pizza go to the doctor? Because it was feeling a little saucy!'}, next=('generate_explanation',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7a2f-60ea-8001-4ac26c539f8d'}}, metadata={'source': 'loop', 'step': 1, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:56:39.402519+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7232-6cb1-8000-f71609e6cec5'}}, tasks=(PregelTask(id='1f8e036d-b599-6074-0dac-c200937328d4', name='generate_explanation', path=('__pregel_pull', 'generate_explanation'), error=None, interrupts=(), state=None, result={'explanation': 'This joke plays on the double meaning of the word \"saucy.\" In one sense, \"saucy\" can mean bold, impertinent, or sassy. But in the context of a pizza, \"saucy\" refers to the tomato sauce typically used as a base on pizzas. So when the pizza went to the doctor because it was feeling \"saucy,\" it implies that the pizza was not feeling well due to too much sauce, rather than being bold or sassy. The humor comes from the unexpected twist on the word\\'s meaning.'}),), interrupts=()),\n", " StateSnapshot(values={'topic': 'pizza'}, next=('generate_joke',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7232-6cb1-8000-f71609e6cec5'}}, metadata={'source': 'loop', 'step': 0, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:56:38.565188+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7230-65a8-bfff-0a96c2fc4e11'}}, tasks=(PregelTask(id='dcd96e38-1f32-5ed6-9f44-fa2b22c193f0', name='generate_joke', path=('__pregel_pull', 'generate_joke'), error=None, interrupts=(), state=None, result={'joke': 'Why did the pizza go to the doctor? Because it was feeling a little saucy!'}),), interrupts=()),\n", " StateSnapshot(values={}, next=('__start__',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f06cc6e-7230-65a8-bfff-0a96c2fc4e11'}}, metadata={'source': 'input', 'step': -1, 'parents': {}, 'thread_id': '1'}, created_at='2025-07-29T21:56:38.564189+00:00', parent_config=None, tasks=(PregelTask(id='2d4d902e-419f-6dff-1b83-385121fc185e', name='__start__', path=('__pregel_pull', '__start__'), error=None, interrupts=(), state=None, result={'topic': 'pizza'}),), interrupts=())]"]}, "execution_count": 116, "metadata": {}, "output_type": "execute_result"}], "source": ["list(workflow.get_state_history(config1))"]}, {"cell_type": "markdown", "id": "d8435127", "metadata": {}, "source": ["### Fault Tolerance"]}, {"cell_type": "code", "execution_count": 2, "id": "b7aacf7e", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph, END\n", "from langgraph.checkpoint.memory import InMemorySaver\n", "from typing import TypedDict\n", "import time"]}, {"cell_type": "code", "execution_count": 3, "id": "095de12d", "metadata": {}, "outputs": [], "source": ["# 1. Define the state\n", "class CrashState(TypedDict):\n", "    input: str\n", "    step1: str\n", "    step2: str"]}, {"cell_type": "code", "execution_count": 4, "id": "13365a36", "metadata": {}, "outputs": [], "source": ["# 2. Define steps\n", "def step_1(state: CrashState) -> CrashState:\n", "    print(\"✅ Step 1 executed\")\n", "    return {\"step1\": \"done\", \"input\": state[\"input\"]}\n", "\n", "def step_2(state: CrashState) -> CrashState:\n", "    print(\"⏳ Step 2 hanging... now manually interrupt from the notebook toolbar (STOP button)\")\n", "    time.sleep(1000)  # Simulate long-running hang\n", "    return {\"step2\": \"done\"}\n", "\n", "def step_3(state: CrashState) -> CrashState:\n", "    print(\"✅ Step 3 executed\")\n", "    return {\"done\": True}"]}, {"cell_type": "code", "execution_count": 5, "id": "2838a3a1", "metadata": {}, "outputs": [], "source": ["# 3. Build the graph\n", "builder = StateGraph(CrashState)\n", "builder.add_node(\"step_1\", step_1)\n", "builder.add_node(\"step_2\", step_2)\n", "builder.add_node(\"step_3\", step_3)\n", "\n", "builder.set_entry_point(\"step_1\")\n", "builder.add_edge(\"step_1\", \"step_2\")\n", "builder.add_edge(\"step_2\", \"step_3\")\n", "builder.add_edge(\"step_3\", END)\n", "\n", "checkpointer = InMemorySaver()\n", "graph = builder.compile(checkpointer=checkpointer)"]}, {"cell_type": "code", "execution_count": null, "id": "17c012e7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["▶️ Running graph: Please manually interrupt during Step 2...\n", "✅ Step 1 executed\n", "⏳ Step 2 hanging... now manually interrupt from the notebook toolbar (STOP button)\n"]}], "source": ["try:\n", "    print(\"▶️ Running graph: Please manually interrupt during Step 2...\")\n", "    graph.invoke({\"input\": \"start\"}, config={\"configurable\": {\"thread_id\": 'thread-1'}})\n", "except KeyboardInterrupt:\n", "    print(\"❌ <PERSON><PERSON> manually interrupted (crash simulated).\")"]}, {"cell_type": "code", "execution_count": null, "id": "95df3f98", "metadata": {}, "outputs": [], "source": ["# 6. Re-run to show fault-tolerant resume\n", "print(\"\\n🔁 Re-running the graph to demonstrate fault tolerance...\")\n", "final_state = graph.invoke(None, config={\"configurable\": {\"thread_id\": 'thread-1'}})\n", "print(\"\\n✅ Final State:\", final_state)"]}, {"cell_type": "code", "execution_count": null, "id": "4f61ccbd", "metadata": {}, "outputs": [], "source": ["list(graph.get_state_history({\"configurable\": {\"thread_id\": 'thread-1'}}))"]}], "metadata": {"kernelspec": {"display_name": "myenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 5}